prefix=/Users/<USER>/ffmpeg-iOS/thin/arm64
exec_prefix=${prefix}
libdir=/Users/<USER>/ffmpeg-iOS/thin/arm64/lib
includedir=/Users/<USER>/ffmpeg-iOS/thin/arm64/include

Name: libavcodec
Description: FFmpeg codec library
Version: 58.35.100
Requires: libswresample >= 3.3.100, libavutil >= 56.22.100
Requires.private: 
Conflicts:
Libs: -L${libdir}  -lavcodec -liconv -lm -lz -framework AudioToolbox -pthread -framework VideoToolbox -framework CoreFoundation -framework CoreMedia -framework CoreVideo
Libs.private: 
Cflags: -I${includedir}
