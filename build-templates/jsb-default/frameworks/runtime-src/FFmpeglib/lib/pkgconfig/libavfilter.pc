prefix=/Users/<USER>/ffmpeg-iOS/thin/arm64
exec_prefix=${prefix}
libdir=/Users/<USER>/ffmpeg-iOS/thin/arm64/lib
includedir=/Users/<USER>/ffmpeg-iOS/thin/arm64/include

Name: libavfilter
Description: FFmpeg audio/video filtering library
Version: 7.40.101
Requires: libswscale >= 5.3.100, libavformat >= 58.20.100, libavcodec >= 58.35.100, libswresample >= 3.3.100, libavutil >= 56.22.100
Requires.private: 
Conflicts:
Libs: -L${libdir}  -lavfilter -pthread -lm
Libs.private: 
Cflags: -I${includedir}
