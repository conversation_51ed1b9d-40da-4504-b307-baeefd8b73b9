prefix=/Users/<USER>/ffmpeg-iOS/thin/arm64
exec_prefix=${prefix}
libdir=/Users/<USER>/ffmpeg-iOS/thin/arm64/lib
includedir=/Users/<USER>/ffmpeg-iOS/thin/arm64/include

Name: libavformat
Description: FFmpeg container format library
Version: 58.20.100
Requires: libavcodec >= 58.35.100, libswresample >= 3.3.100, libavutil >= 56.22.100
Requires.private: 
Conflicts:
Libs: -L${libdir}  -lavformat -lm -lbz2 -lz -Wl,-framework,CoreFoundation -Wl,-framework,Security
Libs.private: 
Cflags: -I${includedir}
