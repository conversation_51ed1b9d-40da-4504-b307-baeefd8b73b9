#ifndef _JSB_LIVEVIDEO_H_
#define _JSB_LIVEVIDEO_H_

#include "cocos/scripting/js-bindings/jswrapper/SeApi.h"

extern se::Object* __jsb_LiveVideo_proto;
extern se::Class* __jsb_LiveVideo_class;

SE_DECLARE_FUNC(js_LiveVideo_finalize);
SE_DECLARE_FUNC(js_LiveVideo_constructor);

SE_DECLARE_FUNC(js_LiveVideo_getFilePath);
SE_DECLARE_FUNC(js_LiveVideo_setFilePath);
SE_DECLARE_FUNC(js_LiveVideo_mediaStatus);
SE_DECLARE_FUNC(js_LiveVideo_playMedia);
SE_DECLARE_FUNC(js_LiveVideo_pauseMedia);
SE_DECLARE_FUNC(js_LiveVideo_stopMedia);
SE_DECLARE_FUNC(js_LiveVideo_seekToPosition);
SE_DECLARE_FUNC(js_LiveVideo_retain);
SE_DECLARE_FUNC(js_LiveVideo_release);
SE_DECLARE_FUNC(js_LiveVideo_getPixelData);
SE_DECLARE_FUNC(js_LiveVideo_getPixelSize);
SE_DECLARE_FUNC(js_LiveVideo_getPixelWidth);
SE_DECLARE_FUNC(js_LiveVideo_getPixelHeight);
SE_DECLARE_FUNC(js_LiveVideo_enableRealTime);
SE_DECLARE_FUNC(js_LiveVideo_disableRealTime);
SE_DECLARE_FUNC(js_LiveVideo_enableInfiniteBuffer);
SE_DECLARE_FUNC(js_LiveVideo_disableInfiniteBuffer);
SE_DECLARE_FUNC(js_LiveVideo_enableFramedrop);
SE_DECLARE_FUNC(js_LiveVideo_disableFramedrop);
SE_DECLARE_FUNC(js_LiveVideo_setSyncAndMode);
SE_DECLARE_FUNC(js_LiveVideo_getDebugInfo);
SE_DECLARE_FUNC(js_LiveVideo_getDuration);
SE_DECLARE_FUNC(js_LiveVideo_getTime);
SE_DECLARE_FUNC(js_LiveVideo_setMediaStopTimeOutCb);
SE_DECLARE_FUNC(js_LiveVideo_setMediaReadyTimeOutCb);
SE_DECLARE_FUNC(js_LiveVideo_setMediaPlayTimeOutCb);

bool js_register_LiveVideo(se::Object* global);

#endif
