#ifndef LiveVideo_h
#define LiveVideo_h

#include "SimpleViewRGB.h"

#include "cocos2d.h"
//#include "scripting/js-bindings/event/EventDispatcher.h"

#ifndef INT64_C
#define INT64_C(c) (c ## LL)
#define UINT64_C(c) (c ## ULL)
#endif

USING_NS_CC;
using namespace ffplayer;

class LiveVideo : public SimpleViewRGB {
public:
	LiveVideo();
	virtual ~LiveVideo();
	virtual bool init() override;
	void initEvents();
	virtual void retain() override;
	virtual void release() override;
	//void onChangeXianLu(const CustomEvent& event);
	//void onExitCowboyLiveVideo(const CustomEvent& event);
	//void doExitCowboyLiveVideo();
	//void onLeaveRoomSucc(const CustomEvent& event);
	//void doLeaveRoomSucc();
	//void onBackMainScene(const CustomEvent& event);
	//void doBackMainScene();
	//void onLogout(const CustomEvent& event);
	//void doLogout();
	//void onPlayMedia();
	//void onStopVideo(const CustomEvent& event);
	//void onPlayVideo(const CustomEvent& event);

	//void testUrl(const std::string & url);
	void playMedia();
	virtual void playMedia(const char * streamPath);
	virtual void pauseMedia();
	virtual void stopMedia();
	virtual void renderVideoFrame(void * frameData);
	inline unsigned char* getPixelData() {
		return getBuff();
	}
	inline int getPixelSize() const {
		return _pixel_w * _pixel_h * bytesPerColor; //rgb
	}
	inline int getPixelWidth() const {
		return _pixel_w;
	}
	inline int getPixelHeight() const {
		return _pixel_h;
	}
	inline long long getRenderTime() {
		return renderClk;
	}
	inline void resetRenderTime() {
		renderClk = 0;
	}
	inline std::string getFilePath() const {
		return filePath;
	}
	inline void setFilePath(std::string path) {
		filePath = path;
	}
	inline void setMediaStopTimeOutCb(const std::function<void()>& cb) {
		_msStopTimeOutCb = cb;
	}
	inline void setMediaReadyTimeOutCb(const std::function<void()>& cb) {
		_msReadyTimetOutCb = cb;
	}
	inline void setMediaPlayTimeOutCb(const std::function<void()>& cb) {
		_msPlayingTimeOutCb = cb;
	}
private:
	virtual unsigned char* getBuff();
	virtual void clearBuff();
	std::string filePath;
	bool copybuff;

	void checkMediaStatus(float dt);
	bool playing;
	int curMediaStatus;
	double durationTime;
	long long renderClk;
	//uint32_t eventListener[7];
	std::function<void()> _msStopTimeOutCb;
	std::function<void()> _msReadyTimetOutCb;
	std::function<void()> _msPlayingTimeOutCb;
};

#endif
