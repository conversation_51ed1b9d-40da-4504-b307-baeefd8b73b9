#ifndef __FFSIMPLECONTROLLERRGB_H__
#define __FFSIMPLECONTROLLERRGB_H__

#include "Controller.h"
#include "VideoOutput.h"
#include "SubtitleOutput.h"
#include "SimpleViewRGB.h"
#include "AudioAl.h"
#include "SimpleSynchronizer.h"
#include "MediaState.h"

namespace ffplayer
{
	class SimpleControllerRGB : public Controller
	{
	public:
		SimpleControllerRGB(SimpleViewRGB * simpleView);
		~SimpleControllerRGB();
		virtual void playMedia(const char * streamPath) override;
		virtual void pauseMedia();
		virtual void seekToPosition(float pos);
		virtual void stopMedia();
		virtual int mediaStatus();
		//
		void enableRealTime();
		void disableRealTime();
		void enableInfiniteBuffer();
		void disableInfiniteBuffer();
		void enableFramedrop();
		void disableFramedrop();
		void setSyncAndMode(int syncType, ShowMode mode);
		std::string getDebugInfo();
		double getDuration();
		double getTime();
	protected:
		SimpleViewRGB * view;
		AudioAl * audioOutput;
		SimpleSynchronizer * synchronizer;
	};
}
#endif