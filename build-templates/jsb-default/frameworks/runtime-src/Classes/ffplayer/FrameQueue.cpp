#include "FrameQueue.h"


using namespace ffplayer;

FrameQueue::FrameQueue(PacketQueue *pktq, int max_size, bool keep_last) {
	memset(queue, 0, sizeof(Frame) * FRAME_QUEUE_SIZE);
	this->pktq = pktq;
	this->max_size = FFMIN(max_size, FRAME_QUEUE_SIZE); //FRAME_QUEUE_SIZE; // 
	this->keep_last = keep_last;
	for (int i = 0; i < this->max_size; i++) {
		this->queue[i].frame = av_frame_alloc();
		if (this->queue[i].frame == NULL) {
			FFLOG("FrameQueue av_frame_alloc failed!");
		}
	}
	this->rindex = 0;
	this->windex = 0;
	this->size = 0;
	this->rindex_shown = 0;
}

FrameQueue::~FrameQueue() {
	for (int i = 0; i < this->max_size; i++) {
		Frame *vp = &this->queue[i];
		unrefFrame(vp);
		av_frame_free(&vp->frame);
	}
}

void FrameQueue::unrefFrame(Frame *vp) {
	av_frame_unref(vp->frame);
	avsubtitle_free(&vp->sub);

	//av_freep(&vp->audio_buf1);
	//vp->audio_buf1_size = 0;
	//vp->audio_buf = NULL;
	////vp->audio_buf_size = 0;
}

void FrameQueue::signal() {
	FFLOG("FrameQueue::signal()");
}

Frame *FrameQueue::peek() {
	return &queue[(rindex + rindex_shown) % max_size];
}

Frame *FrameQueue::peekNext() {
	return &queue[(rindex + rindex_shown + 1) % max_size];
}

Frame *FrameQueue::peekLast() {
	return &queue[rindex];
}

Frame *FrameQueue::peekWritable() {
	if (this->pktq->isAbort() || this->size >= this->max_size)
		return NULL;

	return &this->queue[this->windex];
}

Frame * FrameQueue::peekReadable() {
	if (this->pktq->isAbort() || this->size - this->rindex_shown <= 0)
		return NULL;

	return &this->queue[(this->rindex + this->rindex_shown) % this->max_size];
}

void FrameQueue::push() {
	if (this->size >= this->max_size) {
		FFLOG("FrameQueue::push() failed, this->size >= this->max_size");
		return;
	}
	mutex.lock();
	if (++this->windex == this->max_size)
		this->windex = 0;
	this->size++;
	mutex.unlock();
}

void FrameQueue::next()
{
	if (this->size - this->rindex_shown <= 0) {
		FFLOG("FrameQueue::next() failed, this->size - this->rindex_shown <= 0");
		return;
	}
	if (this->keep_last && !this->rindex_shown) {
		this->rindex_shown = 1;
		return;
	}
	mutex.lock();
	unrefFrame(&this->queue[this->rindex]);
	if (++this->rindex == this->max_size)
		this->rindex = 0;
	this->size--;
	mutex.unlock();
}

void FrameQueue::flush() {
	mutex.lock();
	for (int i = 0; i < this->max_size; i++) {
		unrefFrame(&this->queue[i]);
	}
	this->rindex = 0;
	this->windex = 0;
	this->size = 0;
	this->rindex_shown = 0;
	mutex.unlock();
}

int64_t FrameQueue::lastPos()
{
	Frame *fp = peekLast();
	if (this->rindex_shown && fp->serial == this->pktq->getSerial())
		return fp->pos;
	else
		return -1;
}