#ifndef __FFAUDIOOUTPUT_H__
#define __FFAUDIOOUTPUT_H__

namespace ffplayer
{
	class AudioObserver {
	public:
		virtual void fillAudioFrames(void *, int &) = 0;
	};

	class AudioOutput
	{
	public:
		virtual int openAudioDevice(void *) = 0;
		virtual void closeAudioDevice() = 0;
		virtual void pauseAudioDevice() = 0;
		virtual void registObserver(AudioObserver *) = 0;
		virtual void removeObserver(AudioObserver *) = 0;
	};
}
#endif