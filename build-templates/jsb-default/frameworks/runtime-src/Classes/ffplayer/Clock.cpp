#include "Clock.h"
#include <math.h>

#include "Common.h"

extern "C"
{
#include "libavutil/time.h"
};

using namespace ffplayer;

Clock::Clock() {
	init(NULL);
}

Clock::~Clock() {

}

void Clock::init(int *queue_serial) {
	speed = 1.0;
	paused = false;
	this->queue_serial = queue_serial;
	setClock(NAN, -1);
}

double Clock::getClock() {
	if (*queue_serial != serial)
		return NAN;
	if (paused) {
		return pts;
	}
	else {
		double time = av_gettime_relative() / 1000000.0;
		return pts_drift + time - (time - last_updated) * (1.0 - speed);
	}
}

void Clock::setClock(double pts, int serial, double time) {
	this->pts = pts;
	this->last_updated = time;
	this->pts_drift = this->pts - time;
	this->serial = serial;
}

void Clock::setClock(double pts, int serial) {
	double time = av_gettime_relative() / 1000000.0;
	setClock(pts, serial, time);
}

void Clock::setSpeed(double speed) {
	setClock(getClock(), this->serial);
	this->speed = speed;
}

void Clock::syncToSlave(Clock *slave) {
	double clock = getClock();
	double slave_clock = slave->getClock();
	if (!isnan(slave_clock) && (isnan(clock) || fabs(clock - slave_clock) > AV_NOSYNC_THRESHOLD)) {
		setClock(slave_clock, slave->getSerial());
	}
}