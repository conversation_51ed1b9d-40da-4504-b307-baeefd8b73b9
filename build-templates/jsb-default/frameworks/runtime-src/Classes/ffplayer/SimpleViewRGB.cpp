#include "SimpleViewRGB.h"
#include "SimpleControllerRGB.h"

using namespace ffplayer;

SimpleViewRGB::SimpleViewRGB() {
}

SimpleViewRGB::~SimpleViewRGB() {
	//stopMedia();
	//delete (SimpleControllerRGB *)controller;
	if (autoRelease) {
		release();
	}
	FFLOG("SimpleViewRGB::~SimpleViewRGB()");
}

void SimpleViewRGB::playMedia(const char * streamPath) {
	buffState = 0;
	controller->playMedia(streamPath);
}

void SimpleViewRGB::pauseMedia() {
	controller->pauseMedia();
}

void SimpleViewRGB::seekToPosition(float pos) {
	controller->seekToPosition(pos);
}

void SimpleViewRGB::stopMedia() {
	controller->stopMedia();
	clearBuff();
}

int SimpleViewRGB::mediaStatus() {
	return controller->mediaStatus();
}

void SimpleViewRGB::renderVideoFrame(void * frameData) {
	if (buffState == -1) {
		return;
	}
	if (buffState == 1) {
		//++dropFrameCnt;
		//FFLOG("dropFrameCnt = %d", dropFrameCnt);
	}
	buffState = 0;
	mutex.lock();
	Frame * frm = (Frame *)frameData;
	_pixel_bw = frm->width;
	_pixel_bh = frm->height;
	if (!backbuff) {
		backbuff = new unsigned char[_pixel_bw * _pixel_bh * bytesPerColor];
	}
	int64_t nBufsize = 0;
	for (int y = 0; y < _pixel_bh; ++y) { //rgb
		memcpy(backbuff + nBufsize, frm->frame->data[0] + y * frm->frame->linesize[0], _pixel_bw * bytesPerColor);
		nBufsize += _pixel_bw * bytesPerColor;
	}
	buffState = 1;
	mutex.unlock();
}
void SimpleViewRGB::requestVideoFrame() {

}
void SimpleViewRGB::renderSubtitleFrame() {

}
void SimpleViewRGB::requestSubtitleFrame() {

}

bool SimpleViewRGB::init()
{
	bytesPerColor = 3;
	dropFrameCnt = 0;
	buff = nullptr;
	backbuff = nullptr;
	//pos = 0;
	int buffState = -1; //-1 0 1
	_pixel_w = _pixel_h = 0;
	_pixel_bw = _pixel_bh = 0;
	controller = (Controller *)new SimpleControllerRGB(this);
	autoRelease = true;

	return true;
}

void ffplayer::SimpleViewRGB::retain()
{
}

void ffplayer::SimpleViewRGB::release()
{
	stopMedia();
	delete (SimpleControllerRGB *)controller;
	autoRelease = false;
}

//void SimpleViewRGB::update(float dt) {
//	unsigned char* buf = getBuff();
//	if (buf == NULL)
//	{
//		return;
//	}
//	//Texture2D* texture2d = this->getTexture();
//	////texture2d->updateWithData(buf, 0, 0, _pixel_w, _pixel_h);
//	//texture2d->initWithData(buf, _pixel_w * _pixel_h * 3, Texture2D::PixelFormat::RGB888, _pixel_w, _pixel_h, Size(_pixel_w, _pixel_h));
//	//this->setTextureRect(Rect(0.0f, 0.0f, _pixel_w, _pixel_h));
//	//Size visibleSize = Director::getInstance()->getVisibleSize();
//	//this->setScaleX(visibleSize.width / _pixel_w);
//	//this->setScaleY(visibleSize.height / _pixel_h);
//}

void SimpleViewRGB::enableRealTime() {
	((SimpleControllerRGB *)controller)->enableRealTime();
}

void SimpleViewRGB::disableRealTime() {
	((SimpleControllerRGB *)controller)->disableRealTime();
}

void SimpleViewRGB::enableInfiniteBuffer() {
	((SimpleControllerRGB *)controller)->enableInfiniteBuffer();
}

void SimpleViewRGB::disableInfiniteBuffer() {
	((SimpleControllerRGB *)controller)->disableInfiniteBuffer();
}

void SimpleViewRGB::enableFramedrop() {
	((SimpleControllerRGB *)controller)->enableFramedrop();
}
void SimpleViewRGB::disableFramedrop() {
	((SimpleControllerRGB *)controller)->disableFramedrop();
}

void SimpleViewRGB::setSyncAndMode(int syncType, ShowMode mode) {
	((SimpleControllerRGB *)controller)->setSyncAndMode(syncType, mode);
}

std::string SimpleViewRGB::getDebugInfo() {
	return ((SimpleControllerRGB *)controller)->getDebugInfo();
}

double SimpleViewRGB::getDuration() {
	return ((SimpleControllerRGB *)controller)->getDuration();
}

double SimpleViewRGB::getTime() {
	return ((SimpleControllerRGB *)controller)->getTime();
}

unsigned char* SimpleViewRGB::getBuff()
{
	if (buffState == -1) {
		return NULL;
	}
	if (buffState == 1) {
		mutex.lock();
		unsigned char * tmp = buff;
		buff = backbuff;
		backbuff = tmp;
		_pixel_w = _pixel_bw;
		_pixel_h = _pixel_bh;
		buffState = 0;
		mutex.unlock();
	}
	return buff;
}

void SimpleViewRGB::clearBuff() {
	buffState = -1;
	mutex.lock();
	if (buff) {
		delete[] buff;
		buff = NULL;
	}
	if (backbuff) {
		delete[] backbuff;
		backbuff = NULL;
	}
	mutex.unlock();
}