#include "SimpleControllerRGB.h"

using namespace ffplayer;


SimpleControllerRGB::SimpleControllerRGB(SimpleViewRGB * simpleView) {
	view = simpleView;
	audioOutput = new AudioAl();
	synchronizer = new SimpleSynchronizer((VideoOutput*)view, (AudioOutput *)audioOutput, (SubtitleOutput*)view);
	//synchronizer->disableAudio();
	synchronizer->disableSubtitle();
	//synchronizer->disableVideo();
	//synchronizer->disableFramedrop();
	//synchronizer->disableReorderPTS();
	//synchronizer->enableSampleDisplay();
	//synchronizer->enableInfiniteBuffer();
	//AV_SYNC_AUDIO_MASTER,
	//AV_SYNC_VIDEO_MASTER,
	//AV_SYNC_EXTERNAL_CLOCK,
	synchronizer->enableRealTime(0.0);
	//synchronizer->setStartTime(2);
	//synchronizer->setDelayTime(2.0);
	synchronizer->setSyncAndMode(AV_SYNC_EXTERNAL_CLOCK, ShowMode::SHOW_MODE_VIDEO);
	synchronizer->setPixelFormat(2);//2=rgb, 25=argb
}

SimpleControllerRGB::~SimpleControllerRGB() {
	delete synchronizer;
	//delete audioOutput;
	audioOutput->release();
}

void SimpleControllerRGB::playMedia(const char * streamPath) {
	synchronizer->playMedia(streamPath);
}

void SimpleControllerRGB::pauseMedia() {
	synchronizer->pauseMedia();
}

void SimpleControllerRGB::seekToPosition(float pos) {
	synchronizer->seekToPosition(pos);
}

void SimpleControllerRGB::stopMedia() {
	synchronizer->stopMedia();
}

int SimpleControllerRGB::mediaStatus() {
	return synchronizer->mediaStatus();
}
//
//
void SimpleControllerRGB::enableRealTime() {
	synchronizer->enableRealTime(0.0);
}

void SimpleControllerRGB::disableRealTime() {
	synchronizer->disableRealTime();
}

void SimpleControllerRGB::enableInfiniteBuffer() {
	synchronizer->enableInfiniteBuffer();
}

void SimpleControllerRGB::disableInfiniteBuffer() {
	synchronizer->disableInfiniteBuffer();
}

void SimpleControllerRGB::enableFramedrop() {
	synchronizer->enableFramedrop();
}
void SimpleControllerRGB::disableFramedrop() {
	synchronizer->disableFramedrop();
}

void SimpleControllerRGB::setSyncAndMode(int syncType, ShowMode mode) {
	synchronizer->setSyncAndMode(syncType, mode);
}

std::string SimpleControllerRGB::getDebugInfo() {
	return synchronizer->getDebugInfo();
}

double SimpleControllerRGB::getDuration() {
	return synchronizer->getDuration();
}

double SimpleControllerRGB::getTime() {
	return synchronizer->getTime();
}