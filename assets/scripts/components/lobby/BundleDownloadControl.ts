import * as pf from '../../../poker-framework/scripts/pf';
import { ProgressBarControl } from './ProgressBarControl';

const { ccclass, property } = cc._decorator;

// eslint-disable-next-line no-shadow
export enum BUNDlE_DOWNLOAD_STATE {
    NEED_TO_DOWNLOAD = 'NEED_TO_DOWNLOAD',
    NEED_TO_UPDATE = 'NEED_TO_UPDATE',
    WAITING_FOR_DOWNLOAD = 'WAITING_FOR_DOWNLOAD',
    DOWNLOADING = 'DOWNLOADING',
    DOWNLOAD_FAILED = 'DOWNLOAD_FAILED',
    CANNOT_DOWNLOAD = 'CANNOT_DOWNLOAD',
    DOWNLOADED = 'DOWNLOADED'
}

const DEPENDENT_BUNDLE_NAME = 'common-resource';

@ccclass
export default class BundleDownloadControl extends cc.Component {
    @property(ProgressBarControl)
    private progressBar: ProgressBarControl = null;
    private _isInitialized: boolean = false;
    private _bundleName: string = '';
    private _updateItem: pf.UpdateItem = null;
    private _isBundle = false;
    private _isBundleValid = true;
    private __downloadState: BUNDlE_DOWNLOAD_STATE;
    private _currentUpdatePercentage = 0;
    private _completeCallback: () => void = null;
    private _failedCallback: () => void = null;

    private _label: cc.Label = null;

    get _downloadState() {
        return this.__downloadState;
    }

    set _downloadState(state) {
        this.__downloadState = state;
        if (!this._label) {
            const node = new cc.Node();
            const label = node.addComponent(cc.Label);
            node.color = cc.Color.RED;
            node.anchorX = node.anchorY = 0;
            label.fontSize = 20;
            label.horizontalAlign = cc.Label.HorizontalAlign.LEFT;
            this._label = label;
            const widget = node.addComponent(cc.Widget);
            widget.isAlignLeft = widget.isAlignTop = true;
            widget.left = widget.top = 10;
            this.node.addChild(node);
        }

        this._label.string = BUNDlE_DOWNLOAD_STATE[state];
    }

    get isBundle(): boolean {
        return this._isBundle;
    }

    get isBundleValid(): boolean {
        return this._isBundleValid;
    }

    get downloadState(): BUNDlE_DOWNLOAD_STATE {
        return this._downloadState;
    }

    get isUpdating(): boolean {
        return this._downloadState === BUNDlE_DOWNLOAD_STATE.DOWNLOADING;
    }

    get isQueuing(): boolean {
        return this._downloadState === BUNDlE_DOWNLOAD_STATE.WAITING_FOR_DOWNLOAD;
    }

    // LIFE-CYCLE CALLBACKS:

    protected onDestroy(): void {
        if (this.isBundle) {
            this._updateItem.setProgressCallback(null);
            this.unregisterEvent();
        }
    }

    // CUSTOM METHODS:

    private registerEvent(): void {
        pf.updateManager.addListener(pf.BUNDLE_EVENT.BUNDLE_LOADED, this.onBundleLoaded);
    }

    private unregisterEvent(): void {
        pf.updateManager.removeListener(pf.BUNDLE_EVENT.BUNDLE_LOADED, this.onBundleLoaded);
    }

    private onBundleLoaded = (loadedUpdateItem: pf.UpdateItem): void => {
        if (!loadedUpdateItem.progressInfo) {
            return;
        }

        const isIdentifiable = loadedUpdateItem.progressInfo.updateList.includes(this._updateItem.bundle);
        const isDependency = loadedUpdateItem === this.getDependent();
        const isUpToDate = loadedUpdateItem.isUpToDate;

        if (this.isUpdating || isIdentifiable) {
            cc.log('[BundleDownloadControl] onBundleLoaded');
            cc.log('[BundleDownloadControl] isIdentifiable:', isIdentifiable);
            cc.log('[BundleDownloadControl] isDependency:', isDependency);
            cc.log('[BundleDownloadControl] isUpToDate:', isUpToDate);

            this.onComplete(loadedUpdateItem);
        } else if (isDependency && isUpToDate) {
            this.updateDownloadState();
        }
    };

    init(bundleName: string): void {
        if (this._isInitialized) {
            return;
        }

        this.initBundleInfo(bundleName);

        if (this.isBundle) {
            this.setDependency();
            this.updateDownloadState();
            this.registerEvent();
            this.restoreProgressCallback();
        }

        this._isInitialized = true;
    }

    private initBundleInfo(bundleName: string): void {
        console.log('[BundleDownloadControl] initBundleInfo. bundleName', bundleName);
        this._bundleName = bundleName;
        this._updateItem = pf.updateManager.getUpdateItem(this._bundleName);
        console.log('[BundleDownloadControl] initBundleInfo. updateItem', this._updateItem);
        if (this._updateItem) {
            this._isBundle = true;
            this._isBundleValid = true;
        } else {
            this._isBundle = false;
            this._isBundleValid = false;
        }
    }

    setProgressBar(progressBar: ProgressBarControl): void {
        this.progressBar = progressBar;
    }

    setCompleteCallback(callback: () => void): void {
        this._completeCallback = callback;
    }

    setFailedCallback(callback: () => void): void {
        this._failedCallback = callback;
    }

    private setDependency(): void {
        const dependentUpdateItem = this.getDependent();
        this._updateItem.setDependency(dependentUpdateItem);
    }

    private updateDownloadState(): void {
        if (this.getDependent().isUpToDate && this._updateItem.isUpToDate) {
            this._downloadState = BUNDlE_DOWNLOAD_STATE.DOWNLOADED;
            return;
        }

        if (!this.isBundleInfoValid()) {
            this._isBundleValid = false;
            this._downloadState = BUNDlE_DOWNLOAD_STATE.CANNOT_DOWNLOAD;
            return;
        }

        if (this._updateItem.isQueuing) {
            this._downloadState = BUNDlE_DOWNLOAD_STATE.WAITING_FOR_DOWNLOAD;
            return;
        }

        if (this.isBundleDownloaded()) {
            this._downloadState = BUNDlE_DOWNLOAD_STATE.NEED_TO_UPDATE;
        } else {
            this._downloadState = BUNDlE_DOWNLOAD_STATE.NEED_TO_DOWNLOAD;
        }
    }

    private restoreProgressCallback(): void {
        const progressInfo = this._updateItem.progressInfo;
        if (progressInfo) {
            const updateItems = progressInfo.updateList.map((bundleName) => pf.updateManager.getUpdateItem(bundleName));
            let isQueuing = true;
            updateItems.forEach((updateItem) => {
                updateItem.setProgressCallback(this.onProgress);
                if (updateItem.isUpdating) {
                    isQueuing = false;
                }
            });

            if (isQueuing) {
                this._downloadState = BUNDlE_DOWNLOAD_STATE.WAITING_FOR_DOWNLOAD;
            } else {
                this._downloadState = BUNDlE_DOWNLOAD_STATE.DOWNLOADING;
            }
        }
    }

    private getDependent(): pf.UpdateItem {
        return DEPENDENT_BUNDLE_NAME ? pf.updateManager.getUpdateItem(DEPENDENT_BUNDLE_NAME) : null;
    }

    private isBundleInfoValid(): boolean {
        const remoteBundleInfo = pf.updateManager.remoteManifest.getBundleInfo(this._bundleName);
        const localBundleInfo = pf.updateManager.localManifest.getBundleInfo(this._bundleName);
        const isRemoteBundleInfoValid = remoteBundleInfo && remoteBundleInfo.md5 ? true : false;
        const isLocalBundleInfoValid = localBundleInfo && localBundleInfo.md5 ? true : false;
        const hasBundleServerAddress = pf.updateManager.localManifest.bundleServerAddress ? true : false;
        return isRemoteBundleInfoValid || (isLocalBundleInfoValid && hasBundleServerAddress);
    }

    private isBundleDownloaded(): boolean {
        const localBundleInfo = pf.updateManager.localManifest.getBundleInfo(this._bundleName);
        return localBundleInfo.version && localBundleInfo.md5 ? true : false;
    }

    shouldDownloadBundle(): boolean {
        const gameUpdateItem = this._updateItem;
        const dependent = this.getDependent();
        if (!gameUpdateItem || !dependent) {
            return false;
        }
        return !gameUpdateItem.isUpToDate || !dependent.isUpToDate;
    }

    download(): Promise<void> {
        if (this.shouldSkipDownload()) {
            return;
        }

        this._downloadState = BUNDlE_DOWNLOAD_STATE.DOWNLOADING;
        this._currentUpdatePercentage = 0;

        const updateItemsToUpdate = this.getUpdateItemsToUpdate();
        if (updateItemsToUpdate.length) {
            const progressInfo = this.getDefaultProgressInfo();
            progressInfo.total = updateItemsToUpdate.length;
            progressInfo.updateList = updateItemsToUpdate.map((updateItem) => updateItem.bundle);

            if (pf.updateManager.isUpdating) {
                cc.log('[BundleDownloadControl] UpdateManager is busy, queuing this update.');
                this._downloadState = BUNDlE_DOWNLOAD_STATE.WAITING_FOR_DOWNLOAD;
                this.prepareDownloadUI();
                this._updateItem.isQueuing = true;
            }

            updateItemsToUpdate.forEach((updateItem) => {
                cc.log('[BundleDownloadControl] Enqueue:', updateItem.bundle);
                updateItem.progressInfo = progressInfo;
                updateItem.setProgressCallback(this.onProgress);
                pf.updateManager.download(updateItem);
            });
        }
    }

    prepareDownloadUI(): void {
        this.progressBar.show();
        this.progressBar.onLoadingProgress(0);
    }

    private shouldSkipDownload(): boolean {
        if (!this._updateItem) {
            cc.warn('[BundleDownloadControl] Skip download, invalid UpdateItem.');
            return true;
        }
        if (this.isUpdating) {
            cc.warn('[BundleDownloadControl] Skip download, is downloading.');
            return true;
        }
        if (this.isQueuing) {
            cc.warn('[BundleDownloadControl] Skip download, is waiting for download.');
            return true;
        }

        return false;
    }

    private getDefaultProgressInfo(): pf.IProgressInfo {
        return {
            loaded: 0,
            total: 0,
            updateList: []
        };
    }

    private getUpdateItemsToUpdate(): pf.UpdateItem[] {
        const dependent = this.getDependent();
        const game = this._updateItem;

        if (pf.updateManager.isUpdating) {
            return !game.isUpToDate ? [game] : [];
        }
        return [dependent, game].filter((updateItem) => updateItem && !updateItem.isUpToDate);
    }

    private onProgress = (
        downloadBytes: number,
        totalBytes: number,
        percentage: number,
        progressInfo: pf.IProgressInfo
    ): void => {
        this.progressBar.show();
        this._downloadState = BUNDlE_DOWNLOAD_STATE.DOWNLOADING;
        const percent = Math.min(1, percentage / progressInfo.total + progressInfo.loaded / progressInfo.total);

        if (percent < this._currentUpdatePercentage) {
            return;
        }

        this._currentUpdatePercentage = percent;
        this.progressBar.onLoadingProgress(percent);
    };

    private onComplete(updateItem: pf.UpdateItem): void {
        cc.log('[BundleDownloadControl] Download onComplete.');
        if (!this) {
            return;
        }

        if (updateItem === this.getDependent()) {
            this.dependencyUpdated(updateItem);
        } else if (updateItem === this._updateItem) {
            this.gameUpdated();
        }

        const progressInfo = updateItem.progressInfo;
        if (progressInfo.loaded === progressInfo.total) {
            updateItem.progressInfo = null;
        }
    }

    private dependencyUpdated(updateItem: pf.UpdateItem): void {
        if (updateItem.isUpToDate) {
            cc.log('[BundleDownloadControl] common-resource is updated.');
            if (this._updateItem.isUpToDate) {
                this.progressBar.hide();
                this._downloadState = BUNDlE_DOWNLOAD_STATE.DOWNLOADED;
                if (this._completeCallback) {
                    this._completeCallback();
                }
            }
        } else {
            cc.error(`[BundleDownloadControl] Failed to update, result: ${pf.UpdateState[updateItem.state]}`);

            this._downloadState = BUNDlE_DOWNLOAD_STATE.DOWNLOAD_FAILED;
            if (this._updateItem.isUpToDate) {
                this.progressBar.hide();
                if (this._failedCallback) {
                    this._failedCallback();
                }
            }
        }
    }

    private gameUpdated(): void {
        this.progressBar.hide();
        if (this._updateItem.isUpToDate) {
            cc.log(`[BundleDownloadControl] ${this._bundleName} is updated.`);
            this._downloadState = BUNDlE_DOWNLOAD_STATE.DOWNLOADED;
            if (this._completeCallback) {
                this._completeCallback();
            }
        } else {
            cc.error(`[BundleDownloadControl] Failed to update, result: ${pf.UpdateState[this._updateItem.state]}`);
            this._downloadState = BUNDlE_DOWNLOAD_STATE.DOWNLOAD_FAILED;
            if (this._failedCallback) {
                this._failedCallback();
            }
        }
    }
}
