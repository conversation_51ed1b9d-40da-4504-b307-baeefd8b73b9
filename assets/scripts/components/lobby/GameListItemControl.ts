// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

const { ccclass, property } = cc._decorator;

import * as pf from '../../../poker-framework/scripts/pf';
import BundleDownloadControl from './BundleDownloadControl';

export enum GameListItemEvent {
    EnterGame = 'EnterGame'
}

@ccclass
export class GameListItemControl extends cc.Component {
    @property(cc.String)
    gameName: string = '';

    @property({
        type: cc.Enum(pf.client.GameId)
    })
    gameId = pf.client.GameId.HumanBoy;

    @property(cc.String)
    bundle: string = '';

    @property(cc.Label)
    gameNameLabel: cc.Label = null;

    @property(cc.Sprite)
    needDownloadSprite: cc.Sprite = null;

    @property(cc.Sprite)
    downloadFailSprite: cc.Sprite = null;

    @property(cc.Sprite)
    cannotDownloadSprite: cc.Sprite = null;

    @property(BundleDownloadControl)
    private bundleDownloadControl: BundleDownloadControl = null;

    private _button: cc.Button = null;

    // LIFE-CYCLE CALLBACKS:

    onLoad() {
        // delay init
        setTimeout(() => {
            this.gameNameLabel.string = this.gameName;
            this.node.on('click', this.onClicked.bind(this));
            this._button = this.getComponent(cc.Button);
            console.log('[GameListItemControl] onLoad. bundleName', this.bundle);
            this.bundleDownloadControl && this.bundleDownloadControl.init(this.bundle);
        }, 500);
    }

    // start() {}
    // update (dt) {}

    // CUSTOM METHODS:

    async onClicked(): Promise<void> {
        console.log('[GameListItemControl] onClick. isBundle', this.bundleDownloadControl.isBundle);

        if (this.bundleDownloadControl.isBundle) {
            this.onClickRemoteBundleItem();
        } else {
            this.onClickInlineBundleItem();
        }
    }

    private onClickRemoteBundleItem(): void {
        console.log('[GameListItemControl] onClickRemoteBundleItem.');
        if (!this.bundleDownloadControl.isBundleValid) {
            // TODO
            return;
        }

        console.log('[GameListItemControl] shouldDownloadBundle.',this.bundleDownloadControl && this.bundleDownloadControl.shouldDownloadBundle());
        if (this.bundleDownloadControl && this.bundleDownloadControl.shouldDownloadBundle()) {
            this.bundleDownloadControl.download();
        } else {
            const event = new cc.Event.EventCustom(GameListItemEvent.EnterGame, true);
            this.node.dispatchEvent(event);
        }
    }

    private onClickInlineBundleItem(): void {
        const event = new cc.Event.EventCustom(GameListItemEvent.EnterGame, true);
        this.node.dispatchEvent(event);
    }
}
