
import * as pf from '../../../poker-framework/scripts/pf';
const { ccclass, property } = cc._decorator;

@ccclass
export default class UpdatePanel extends cc.Component {

    @property({
        type: cc.Label,
        tooltip: '显示更新状态的主要信息文本'
    })
    info: cc.Label = null;

    @property({
        type: cc.ProgressBar,
        tooltip: '文件进度的进度条'
    })
    fileProgress: cc.ProgressBar = null;

    @property({
        type: cc.Label,
        tooltip: '文件进度的文本标签 (例如: 1/10)'
    })
    fileLabel: cc.Label = null;

    @property({
        type: cc.ProgressBar,
        tooltip: '字节进度的进度条'
    })
    byteProgress: cc.ProgressBar = null;

    @property({
        type: cc.Label,
        tooltip: '字节进度的文本标签 (例如: 1.05MB/2.30MB)'
    })
    byteLabel: cc.Label = null;

    @property({
        type: cc.Node,
        tooltip: '关闭按钮'
    })
    close: cc.Node = null;

    @property({
        type: cc.Node,
        tooltip: '检查更新按钮'
    })
    checkBtn: cc.Node = null;

    @property({
        type: cc.Node,
        tooltip: '重试按钮'
    })
    retryBtn: cc.Node = null;

    @property({
        type: cc.Node,
        tooltip: '立即更新按钮'
    })
    updateBtn: cc.Node = null;

    onLoad(): void {
        // Register a touch event for the close button.
        // Using an arrow function `() => {}` automatically binds `this`,
        // so we don't need to pass `this` as the third argument.
        if (this.close) {
            this.close.on(cc.Node.EventType.TOUCH_END, () => {
                // Deactivate the parent node of this component's node,
                // effectively hiding the entire panel.
                if (this.node.parent) {
                    this.node.parent.active = false;
                }
            });
        }
    }

    async showMiniGamePanel() {
        await this.loadCommonResourceBundle();
        await this.loadFeatureTestBundle();
    }

    private async loadCommonResourceBundle(): Promise<void> {
        console.log('loadCommonResourceBundle');
        const bundleName = 'common-resource';
        console.log('loadCommonResourceBundle getUpdateItem');
        const updateItem = pf.updateManager.getUpdateItem(bundleName);
        const entry = await pf.updateManager.loadBundle(updateItem);
        // return entry.enter();
    }

    private async loadFeatureTestBundle(): Promise<void> {
        console.log('loadFeatureTestBundle');
        const bundleName = 'feature-test';
        const updateItem = pf.updateManager.getUpdateItem(bundleName);
        console.log('loadFeatureTestBundle loadBundle');
        const entry = await pf.updateManager.loadBundle(updateItem);
        console.log('loadFeatureTestBundle enter');
        return entry.enter();
    }
}