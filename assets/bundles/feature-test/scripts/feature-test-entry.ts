import { registerEntry } from '../../../poker-framework/scripts/pf';
import * as pf from '../../../poker-framework/scripts/pf';
import { macros } from './common/feature-test-macros';

import BUNDLE_NAME = macros.BUNDLE_NAME;

@registerEntry(BUNDLE_NAME)
export class FeatureTestEntry extends pf.BundleEntryBase {
    private _featureMenu: cc.Node;

    constructor() {
        super();
        this.bundleType = pf.BUNDLE_TYPE.BUNDLE_RESOURCE;
    }

    protected getLanguageStringPath() {
        return null;
    }

    protected getAddressableConfigPath() {
        return pf.languageManager.currentLanguage === pf.LANGUAGE_GROUPS.zh_CN
            ? macros.Addressable_Config_Path.ZH_CN
            : macros.Addressable_Config_Path.EN_US;
    }

    async onLoad(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${BUNDLE_NAME} onLoad`);
    }

    async onEnter(options?: pf.IBundleOptions): Promise<void> {
        cc.log(`bundle ${BUNDLE_NAME} onEnter`);

        await this.loadConfigs();

        const assetLoader = new pf.AddressalbeAssetLoader();
        assetLoader.addLoadAddressableGroupTask('feature-test');
        await assetLoader.start(options?.onProgress);

        const prefab = pf.addressableAssetManager.getAsset<cc.Prefab>('feature-test.test-menu');
        this._featureMenu = cc.instantiate(prefab);
        // cc.game.addPersistRootNode(this._featureMenu);
        cc.director.getScene().addChild(this._featureMenu);

        return Promise.resolve();
    }

    async onExit(): Promise<void> {
        cc.log(`bundle ${BUNDLE_NAME} onExit`);
        this._featureMenu.removeFromParent();
        this._featureMenu.destroy();
        this._featureMenu = null;
        pf.bundleManager.releaseAll(this.bundle);
    }

    onUnload(): void {
        cc.log(`bundle ${BUNDLE_NAME} onUnload`);
    }
}
