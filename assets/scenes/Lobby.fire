[{"__type__": "cc.SceneAsset", "_name": "", "_objFlags": 0, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 7}], "_active": false, "_components": [], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_is3DNode": true, "_groupIndex": 0, "groupIndex": 0, "autoReleaseAssets": false, "_id": "b4112a50-a6a7-4fa1-a66d-32729c0d00c3"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}], "_active": true, "_components": [{"__id__": 5}, {"__id__": 6}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [960, 540, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a5esZu+45LA5mBpvttspPD"}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 4}], "_prefab": null, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 960, "height": 640}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e1WoFrQ79G7r4ZuQE3HlNb"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "node": {"__id__": 3}, "_enabled": true, "_cullingMask": 4294967295, "_clearFlags": 7, "_backgroundColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": -1, "_zoomRatio": 1, "_targetTexture": null, "_fov": 60, "_orthoSize": 10, "_nearClip": 1, "_farClip": 4096, "_ortho": true, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_renderStages": 1, "_alignWithScreen": true, "_id": "81GN3uXINKVLeW4+iKSlim"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_designResolution": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_fitWidth": true, "_fitHeight": true, "_id": "59Cd0ovbdF4byw5sbjJDx7"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "29zXboiXFBKoIV4PQ2liTe"}, {"__type__": "cc.Node", "_name": "PersistRoot", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 8}, {"__id__": 11}], "_active": true, "_components": [{"__id__": 260}, {"__id__": 261}], "_prefab": {"__id__": 262}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [960, 540, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "26xBUW3+ZIXLAeDIi9/MQm"}, {"__type__": "cc.Node", "_name": "Update", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [], "_active": true, "_components": [{"__id__": 9}], "_prefab": {"__id__": 10}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "efM7/uxNNHNrxVhFZlH+px"}, {"__type__": "6c9c7tXZ1tHSLspEOnPy0BU", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "useInlineBundle": false, "h5BundleManifest": {"__uuid__": "f6428827-2411-4183-aaf2-c2eb41b98a72"}, "androidBundleManifest": {"__uuid__": "d4ab288b-259f-4517-bd13-685c99f15fa6"}, "iosBundleManifest": {"__uuid__": "4eb3d178-d12b-40d8-8cc3-************"}, "_id": "f8AFrJKQ1Mqq4mgZR7C/jc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "cdbhFoDblHJIqzjDitul8f", "sync": false}, {"__type__": "cc.Node", "_name": "Lobby", "_objFlags": 0, "_parent": {"__id__": 7}, "_children": [{"__id__": 12}, {"__id__": 72}, {"__id__": 225}], "_active": true, "_components": [], "_prefab": {"__id__": 259}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "46gVmbouxHmrVjwNMEUAcj"}, {"__type__": "cc.Node", "_name": "LoginPanel", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [{"__id__": 13}], "_active": true, "_components": [{"__id__": 71}], "_prefab": {"__id__": 258}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a4BNPdGclF8b/QdZ6IngjP"}, {"__type__": "cc.Node", "_name": "layout", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [{"__id__": 14}, {"__id__": 29}, {"__id__": 44}, {"__id__": 55}], "_active": true, "_components": [{"__id__": 67}, {"__id__": 68}, {"__id__": 69}], "_prefab": {"__id__": 70}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 92, "g": 92, "b": 90, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 400, "height": 200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ccfR8EtUFMEbmM6qt84TMg"}, {"__type__": "cc.Node", "_name": "UsernameEditbox", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 15}, {"__id__": 19}, {"__id__": 23}], "_active": true, "_components": [{"__id__": 27}], "_prefab": {"__id__": 28}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 70, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "86T1qda7JGRqzusFGdyXqQ"}, {"__type__": "cc.Node", "_name": "BACKGROUND_SPRITE", "_objFlags": 512, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 16}, {"__id__": 17}], "_prefab": {"__id__": 18}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "066tdnoc9G0J6TWXHzmj5p"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ff0e91c7-55c6-4086-a39f-cb6e457b8c3b"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "c0nBp1FNpMqZTNKHRZ2dIh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 15}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 160, "_originalHeight": 40, "_id": "717CMBs5lAgrBcEoQPbo49"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "bbs4HzEoJByK4CGML//tmL", "sync": false}, {"__type__": "cc.Node", "_name": "TEXT_LABEL", "_objFlags": 512, "_parent": {"__id__": 14}, "_children": [], "_active": true, "_components": [{"__id__": 20}, {"__id__": 21}], "_prefab": {"__id__": 22}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 238, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-118, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0eY7CmvxtHl4Ee5xVpCEzi"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "a5team@0050", "_N$string": "a5team@0050", "_fontSize": 20, "_lineHeight": 25, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": "add9u0DolMV7HiPOj8QmIS"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 2, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_id": "cbHaVn8yhB844X6SoVXZUN"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "894k0bUm5GhJZgmshAcDMb", "sync": false}, {"__type__": "cc.Node", "_name": "PLACEHOLDER_LABEL", "_objFlags": 512, "_parent": {"__id__": 14}, "_children": [], "_active": false, "_components": [{"__id__": 24}, {"__id__": 25}], "_prefab": {"__id__": 26}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 187, "g": 187, "b": 187, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 238, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-118, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "ebQlo9NKFLRrMO0j3sbmUL"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Enter username here...", "_N$string": "Enter username here...", "_fontSize": 20, "_lineHeight": 25, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": "5cdhHHKaRGVqd9IwsBGlte"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 2, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_id": "1dAa60TLZEfq2NJp5X7Tok"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "a2nRQzfCpHQYCfEPUtdxQO", "sync": false}, {"__type__": "cc.EditBox", "_name": "", "_objFlags": 0, "node": {"__id__": 14}, "_enabled": true, "_string": "a5team@0050", "returnType": 0, "maxLength": 256, "_tabIndex": 0, "editingDidBegan": [], "textChanged": [], "editingDidEnded": [], "editingReturn": [], "_N$textLabel": {"__id__": 20}, "_N$placeholderLabel": {"__id__": 24}, "_N$background": {"__id__": 16}, "_N$inputFlag": 5, "_N$inputMode": 6, "_N$stayOnTop": false, "_id": "6c1iwe/zZOpZkDZ6l54Hmn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "17ujXnFFNEI4HG5agaMkHK", "sync": false}, {"__type__": "cc.Node", "_name": "PasswordEditbox", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 30}, {"__id__": 34}, {"__id__": 38}], "_active": true, "_components": [{"__id__": 42}], "_prefab": {"__id__": 43}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "d7zwZWXiVPPY1op7DhaXHm"}, {"__type__": "cc.Node", "_name": "BACKGROUND_SPRITE", "_objFlags": 512, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 31}, {"__id__": 32}], "_prefab": {"__id__": 33}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 240, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f7kXc9YSNAIKJMyKamATMa"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ff0e91c7-55c6-4086-a39f-cb6e457b8c3b"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "29foz5pzlEc5pL87Birlqe"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 30}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 160, "_originalHeight": 40, "_id": "47jejg6NJPFK4u0PEDtxqy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "b9GV21Zw9C+qP00RIPjDVP", "sync": false}, {"__type__": "cc.Node", "_name": "TEXT_LABEL", "_objFlags": 512, "_parent": {"__id__": 29}, "_children": [], "_active": true, "_components": [{"__id__": 35}, {"__id__": 36}], "_prefab": {"__id__": 37}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 238, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-118, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "10WRFOo11ADrH0F5pf+djz"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "●●●●●●", "_N$string": "●●●●●●", "_fontSize": 20, "_lineHeight": 25, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": "a6zH5ZQjhJx42GLsx6WyTF"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 2, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_id": "1fINRp1Q1Cb4xsPLDT51H3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "76qnZcPH9J2rfLoUkBmI0q", "sync": false}, {"__type__": "cc.Node", "_name": "PLACEHOLDER_LABEL", "_objFlags": 512, "_parent": {"__id__": 29}, "_children": [], "_active": false, "_components": [{"__id__": 39}, {"__id__": 40}], "_prefab": {"__id__": 41}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 187, "g": 187, "b": 187, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 238, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-118, 20, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8f2XClCu9IiIAuuXnKWtjv"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Enter password here...", "_N$string": "Enter password here...", "_fontSize": 20, "_lineHeight": 25, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": "9e3gjvAXxA4qabKb+HRM39"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 2, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 158, "_originalHeight": 40, "_id": "31EwBNXiZN2pTFs/UKK7jA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "55p/v2O8VNBo+lVPBhC2q4", "sync": false}, {"__type__": "cc.EditBox", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_string": "654321", "returnType": 0, "maxLength": 256, "_tabIndex": 0, "editingDidBegan": [], "textChanged": [], "editingDidEnded": [], "editingReturn": [], "_N$textLabel": {"__id__": 35}, "_N$placeholderLabel": {"__id__": 39}, "_N$background": {"__id__": 31}, "_N$inputFlag": 0, "_N$inputMode": 6, "_N$stayOnTop": false, "_id": "82I+xAMR1GjZNJC6UMvCL4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "862ssh+0FOI4MWhb0ilQ0P", "sync": false}, {"__type__": "cc.Node", "_name": "LoginButton", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 45}], "_active": true, "_components": [{"__id__": 52}], "_prefab": {"__id__": 54}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -30, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1bdT1UeSBDKpt2gLg4XgNG"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 44}, "_children": [{"__id__": 46}], "_active": true, "_components": [{"__id__": 49}, {"__id__": 50}], "_prefab": {"__id__": 51}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "57K52DVEhBmIqq1lPaoqie"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 45}, "_children": [], "_active": true, "_components": [{"__id__": 47}], "_prefab": {"__id__": 48}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8aqXfws/dHC7v82YvNkPvh"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 46}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "<PERSON><PERSON>", "_N$string": "<PERSON><PERSON>", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "2crsXD38xICpiuFSoBlt70"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "cc3ufLAzdN+5frAXfHvA/2", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "faLQMxPgpOqJ7/3VS1Q/i9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "aezi6eHVFD8pZpcHKbzfGm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "579SLj6gBDsqPFwKbzBGBy", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 53}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 45}, "_id": "6cM5vB1w9H6oqvFhGGogFo"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 12}, "component": "", "_componentId": "aa5461LSq5Mr6EBXJc0/jvc", "handler": "login", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "5dsaakQQVDBrExilWCHkFm", "sync": false}, {"__type__": "cc.Node", "_name": "autoLoginToggle", "_objFlags": 0, "_parent": {"__id__": 13}, "_children": [{"__id__": 56}, {"__id__": 59}, {"__id__": 62}], "_active": true, "_components": [{"__id__": 65}], "_prefab": {"__id__": 66}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [164.419, -74, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9d6GC7zmtBQaRjVP+dD+Z8"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 57}], "_prefab": {"__id__": 58}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 0]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "59TWLIoIZMKbcJynrkwm9j"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6827ca32-0107-4552-bab2-dfb31799bb44"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2es82C6MBCHbe1yexsMo8f"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "0dby/y/7lL7IaG0v+/cMS1", "sync": false}, {"__type__": "cc.Node", "_name": "checkmark", "_objFlags": 512, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 60}], "_prefab": {"__id__": 61}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 28}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "edOMvQEe9IQbvZSu3K8rsn"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 59}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "90004ad6-2f6d-40e1-93ef-b714375c6f06"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_atlas": null, "_id": "87nF8LweBHZquzP+c+iG8/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "b1u8T0tGdNFIdijh+G1IBC", "sync": false}, {"__type__": "cc.Node", "_name": "autoLoginLabel", "_objFlags": 0, "_parent": {"__id__": 55}, "_children": [], "_active": true, "_components": [{"__id__": 63}], "_prefab": {"__id__": 64}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 50.4}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-66.578, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "56afDDb+pFjKEaO6qvzu0U"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 62}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "自动登录", "_N$string": "自动登录", "_fontSize": 25, "_lineHeight": 40, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "faZubQxMVO2LTrz7S1kIU+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "f2QH5Y+LpNEbNvH0YQh+Vm", "sync": false}, {"__type__": "cc.Toggle", "_name": "", "_objFlags": 0, "node": {"__id__": 55}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 56}, "_N$isChecked": true, "toggleGroup": null, "checkMark": {"__id__": 60}, "checkEvents": [], "_id": "f53hrFXwxMwp3CpHWhLyHc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "43xafN6GhHDopQLHT+5qLb", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9bbda31e-ad49-43c9-aaf2-f7d9896bac69"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2dnGvNIexGkZQfClpGVb2I"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 400, "height": 200}, "_resize": 0, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 10, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 10, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "4eK5euTq5H0qoQ+LBzlWh6"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 18, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_id": "3eTycHb3hO8oJUWhl1Xmrr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "e37ZKXy6hG1aUfOmPOSxah", "sync": false}, {"__type__": "aa5461LSq5Mr6EBXJc0/jvc", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "usernameEditBox": {"__id__": 27}, "passwordEditBox": {"__id__": 42}, "loginButton": {"__id__": 52}, "miniGamePanel": {"__id__": 72}, "autoLoginToggle": {"__id__": 65}, "autoLoginLabel": {"__id__": 63}, "platform": 1, "environment": 0, "_id": "4ci5agNUlAtrhy3m1yAzwg"}, {"__type__": "cc.Node", "_name": "MininGamePanel", "_objFlags": 0, "_parent": {"__id__": 11}, "_children": [{"__id__": 73}, {"__id__": 77}, {"__id__": 215}, {"__id__": 228}, {"__id__": 246}], "_active": false, "_components": [{"__id__": 256}], "_prefab": {"__id__": 257}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 100, "g": 100, "b": 100, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 900}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6e2YHkb3dLBYajTblSHXLi"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [], "_active": true, "_components": [{"__id__": 74}, {"__id__": 75}], "_prefab": {"__id__": 76}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 100, "g": 100, "b": 100, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 600, "height": 900}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e4a3Wiul5FUqUWSIgc9zDg"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9bbda31e-ad49-43c9-aaf2-f7d9896bac69"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "751UedLslB8q399LTrFEJY"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 73}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_id": "95H8WXcVFCf4/7NhEDxGFg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 73}, "asset": {"__uuid__": "96083d03-c332-4a3f-9386-d03e2d19e8ee"}, "fileId": "b9dd7OqiNKdqdco2x7+B2y", "sync": false}, {"__type__": "cc.Node", "_name": "GameListView", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 78}, {"__id__": 85}], "_active": true, "_components": [{"__id__": 213}, {"__id__": 83}], "_prefab": {"__id__": 214}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 580, "height": 780}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-0.523, -29.626, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f0OLbU4khBkKGVMbWQf0eb"}, {"__type__": "cc.Node", "_name": "scrollBar", "_objFlags": 512, "_parent": {"__id__": 77}, "_children": [{"__id__": 79}], "_active": true, "_components": [{"__id__": 82}, {"__id__": 210}, {"__id__": 211}], "_prefab": {"__id__": 212}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 12, "height": 780}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 1, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [290, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1bpLRsMC5KjKWzp+qfRHaZ"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 512, "_parent": {"__id__": 78}, "_children": [], "_active": true, "_components": [{"__id__": 80}], "_prefab": {"__id__": 81}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 10, "height": 156.25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-11, -31.25, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "be/Pn32AlCQ5SbXvr0Xo6l"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 79}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5c3bb932-6c3c-468f-88a9-c8c61d458641"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "9bIOJkYKNIzagRPSX3cyH4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 77}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "17i3b9OUpNt4H4QM3ZDVep", "sync": false}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "_scrollView": {"__id__": 83}, "_touching": false, "_opacity": 255, "enableAutoHide": true, "autoHideTime": 1, "_N$handle": {"__id__": 80}, "_N$direction": 1, "_id": "df3u1OjS5CjLjpELvMe8vM"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 84}, "content": {"__id__": 84}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": {"__id__": 82}, "_id": "864MVls8tFI62EyljauOJT"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 512, "_parent": {"__id__": 85}, "_children": [{"__id__": 89}], "_active": true, "_components": [{"__id__": 208}], "_prefab": {"__id__": 209}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 128, "g": 128, "b": 128, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 568, "height": 780}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 390, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2fhN/DDXZOf53LVgRqFYZe"}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 512, "_parent": {"__id__": 77}, "_children": [{"__id__": 84}], "_active": true, "_components": [{"__id__": 86}, {"__id__": 87}], "_prefab": {"__id__": 88}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 568, "height": 780}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-6, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "5bn0librlLUqlNw55YplIX"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 85}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": "37c8gof41OI4nMI2FHMbUC"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 85}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 12, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_id": "e3+RzVw4VKRLtFNpbmSFtP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 77}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "82P0+WowZHZJNje0dxboQy", "sync": false}, {"__type__": "cc.Node", "_name": "layout", "_objFlags": 0, "_parent": {"__id__": 84}, "_children": [{"__id__": 90}, {"__id__": 128}, {"__id__": 166}], "_active": true, "_components": [{"__id__": 204}, {"__id__": 205}, {"__id__": 206}], "_prefab": {"__id__": 207}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 128, "g": 128, "b": 128, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 568, "height": 780}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -390, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "01TnwhWA5HJIv7l70MlpRk"}, {"__type__": "cc.Node", "_name": "GameListItemCowboy", "_objFlags": 0, "_parent": {"__id__": 89}, "_children": [{"__id__": 91}, {"__id__": 98}, {"__id__": 109}], "_active": true, "_components": [{"__id__": 124}, {"__id__": 125}, {"__id__": 126}], "_prefab": {"__id__": 127}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-129, 300, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "27v4qdj61HCIoeOQT4ESl8"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 90}, "_children": [{"__id__": 92}], "_active": true, "_components": [{"__id__": 95}, {"__id__": 96}], "_prefab": {"__id__": 97}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1aGI2v05hMKrFT9/X9bWBb"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 91}, "_children": [], "_active": true, "_components": [{"__id__": 93}], "_prefab": {"__id__": 94}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 230, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c6XK8JRSdGqraDcfSDGM0+"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 92}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Cowboy", "_N$string": "Cowboy", "_fontSize": 30, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "233gUDYTJEO7mlaAo0pmj3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 90}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "6cy8tJmYpM1JzlIJwywTA5", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 91}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "c9j5x57FhBmJ8A6UxYDVj4"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 91}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "b6GTOVjxtCx5sH83rjHXLn"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 90}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "325xvnnZdIzL9nKEDKkUqA", "sync": false}, {"__type__": "cc.Node", "_name": "DownloadIcons", "_objFlags": 0, "_parent": {"__id__": 90}, "_children": [{"__id__": 99}, {"__id__": 102}, {"__id__": 105}], "_active": true, "_components": [], "_prefab": {"__id__": 108}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-87.482, 36.313, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "c2j51KxIJGlIj7aw3TVC4s"}, {"__type__": "cc.Node", "_name": "cloud-download", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [], "_active": false, "_components": [{"__id__": 100}], "_prefab": {"__id__": 101}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b3snznNzZAjZMVv+qYMf+M"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 99}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6dffcfc8-793a-42b3-9c50-090009c95ce8"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "6aC0Da9xRPm6R1bsOqFmrt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 90}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "fef9N/oBVAFrdNtKrqLZbq", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-download-fail", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [], "_active": false, "_components": [{"__id__": 103}], "_prefab": {"__id__": 104}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "17N0lGuXJLab36EM2GsahJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "485a034b-67c9-48f6-90c0-12dc40cc0973"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "17nA0FHRVI+r0z/ZiRIoqO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 90}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "eaoXhSeMhLBZ2gunzdLkov", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-disconnected", "_objFlags": 0, "_parent": {"__id__": 98}, "_children": [], "_active": false, "_components": [{"__id__": 106}], "_prefab": {"__id__": 107}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "adsAomAOpLNbfQ7eEeDs77"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b63eb81d-9677-4731-95ee-a16810ef8d93"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2d37qgf7xEJ6SY6kNUi+zx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 90}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "a0aYrLhM1PN63JYbRX1G5s", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 90}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "31ml3l7SNIN7U5kk70tTmC", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 90}, "_children": [{"__id__": 110}], "_active": false, "_components": [{"__id__": 122}], "_prefab": {"__id__": 123}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "4dPNcZyypCqpNKt03u2ycq"}, {"__type__": "cc.Node", "_name": "panel", "_objFlags": 0, "_parent": {"__id__": 109}, "_children": [{"__id__": 111}, {"__id__": 114}], "_active": true, "_components": [], "_prefab": {"__id__": 121}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "02GhivtZlJTLam2phES2o/"}, {"__type__": "cc.Node", "_name": "barText", "_objFlags": 0, "_parent": {"__id__": 110}, "_children": [], "_active": true, "_components": [{"__id__": 112}], "_prefab": {"__id__": 113}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.143, -32.044, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "f8XlVelJRApog0ZpYKVDC6"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 28, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "47ZsgCZKZA7bYKxvfWdAs4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 109}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "eesvcEnh1BRYEsC64V3sZp", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 110}, "_children": [{"__id__": 115}], "_active": true, "_components": [{"__id__": 118}, {"__id__": 119}], "_prefab": {"__id__": 120}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 221, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.841, -5.779, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9cUUbKNtdESo5DGUu9jhuI"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 512, "_parent": {"__id__": 114}, "_children": [], "_active": true, "_components": [{"__id__": 116}], "_prefab": {"__id__": 117}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-111, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fc+1GuZKlJMYOeYvwjmU9b"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 115}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a072c17b-3759-48c1-861b-2b15a05f697f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "f6I7M70c1O0bHPxT9DA9Kt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 109}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "01Jrx0A3dGm7qhs0zzxMPI", "sync": false}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "_N$totalLength": 0, "_N$barSprite": {"__id__": 116}, "_N$mode": 0, "_N$progress": 0, "_N$reverse": false, "_id": "a17i6DJxpP5ZqTkcBDi5wk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 114}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee9cd1d3-7f34-46bc-a339-55cccd383e08"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "3dDEApK6FCIJ+weSliWa1Q"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 109}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "6aKAke7rtM/7vkkg0WFx/q", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 109}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "adIYsnvxNGdJZZ05q4J1uY", "sync": false}, {"__type__": "66fcdQxu0dEU6jSa0U20wrG", "_name": "", "_objFlags": 0, "node": {"__id__": 109}, "_enabled": true, "progressBar": {"__id__": 118}, "barText": {"__id__": 112}, "loadingPanel": {"__id__": 110}, "_id": "7dbef/p2lPZ5iAjTlJ2U7y"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 109}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "1184C1lIZDf6jeqRBWfYo4", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 90}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$target": {"__id__": 91}, "_id": "508VYmkRFCw4pw3OVQq7KK"}, {"__type__": "aea7epIr11IRpWxOhq9G706", "_name": "", "_objFlags": 0, "node": {"__id__": 90}, "_enabled": true, "gameName": "Cowboy", "gameId": 10, "bundle": "cowboy", "gameNameLabel": {"__id__": 93}, "needDownloadSprite": {"__id__": 100}, "downloadFailSprite": {"__id__": 103}, "cannotDownloadSprite": {"__id__": 106}, "bundleDownloadControl": {"__id__": 126}, "_id": "d9/lUSbn9MHot8ijxYNSCv"}, {"__type__": "10f659LsiJCs6X3ohKgNi/h", "_name": "", "_objFlags": 0, "node": {"__id__": 90}, "_enabled": true, "progressBar": {"__id__": 122}, "_id": "16MC9OfedJbqHvAaDZQfTq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 90}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "21NoKOEtNAKL7R4Sd2dci5", "sync": false}, {"__type__": "cc.Node", "_name": "GameListItemHumanboy", "_objFlags": 0, "_parent": {"__id__": 89}, "_children": [{"__id__": 129}, {"__id__": 136}, {"__id__": 147}], "_active": true, "_components": [{"__id__": 162}, {"__id__": 163}, {"__id__": 164}], "_prefab": {"__id__": 165}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [131, 300, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "3b+JZSID9JAq2wmp7fgxmE"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 128}, "_children": [{"__id__": 130}], "_active": true, "_components": [{"__id__": 133}, {"__id__": 134}], "_prefab": {"__id__": 135}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6bq8aseBBLi7b/zUEukRl7"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 129}, "_children": [], "_active": true, "_components": [{"__id__": 131}], "_prefab": {"__id__": 132}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 230, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "65CdDilqJLGLu3LkTMeiyy"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 130}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Cowboy", "_N$string": "Cowboy", "_fontSize": 30, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "4f7qsjtz9JMKt350rf6BR0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 128}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "6cy8tJmYpM1JzlIJwywTA5", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 129}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "f1dHS1URdOvJ/VNp0rO427"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 129}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "adX0xtSWhD1KzxA4KzNoWa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 128}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "325xvnnZdIzL9nKEDKkUqA", "sync": false}, {"__type__": "cc.Node", "_name": "DownloadIcons", "_objFlags": 0, "_parent": {"__id__": 128}, "_children": [{"__id__": 137}, {"__id__": 140}, {"__id__": 143}], "_active": true, "_components": [], "_prefab": {"__id__": 146}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-87.482, 36.313, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "a9kUzgW/5Aeq9O3RbkyKSm"}, {"__type__": "cc.Node", "_name": "cloud-download", "_objFlags": 0, "_parent": {"__id__": 136}, "_children": [], "_active": false, "_components": [{"__id__": 138}], "_prefab": {"__id__": 139}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cfgzb8x/1E4ZWesn8IRR8j"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 137}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6dffcfc8-793a-42b3-9c50-090009c95ce8"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "1aGXjIl69LjZ1A9MViEfeP"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 128}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "fef9N/oBVAFrdNtKrqLZbq", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-download-fail", "_objFlags": 0, "_parent": {"__id__": 136}, "_children": [], "_active": false, "_components": [{"__id__": 141}], "_prefab": {"__id__": 142}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "1elG1bRdtHK7sA9IKoeRa0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 140}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "485a034b-67c9-48f6-90c0-12dc40cc0973"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "5czuN0ifhBXbDykq79D3VD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 128}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "eaoXhSeMhLBZ2gunzdLkov", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-disconnected", "_objFlags": 0, "_parent": {"__id__": 136}, "_children": [], "_active": false, "_components": [{"__id__": 144}], "_prefab": {"__id__": 145}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2afqgaFiRGtaOlzCO+GYI7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 143}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b63eb81d-9677-4731-95ee-a16810ef8d93"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "a4atLt5ipETLF31HuxcypT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 128}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "a0aYrLhM1PN63JYbRX1G5s", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 128}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "31ml3l7SNIN7U5kk70tTmC", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 128}, "_children": [{"__id__": 148}], "_active": false, "_components": [{"__id__": 160}], "_prefab": {"__id__": 161}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e58iy4Ew5BbrbN6LvtLU3U"}, {"__type__": "cc.Node", "_name": "panel", "_objFlags": 0, "_parent": {"__id__": 147}, "_children": [{"__id__": 149}, {"__id__": 152}], "_active": true, "_components": [], "_prefab": {"__id__": 159}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "faANsWkTJH05QowQlLi4eU"}, {"__type__": "cc.Node", "_name": "barText", "_objFlags": 0, "_parent": {"__id__": 148}, "_children": [], "_active": true, "_components": [{"__id__": 150}], "_prefab": {"__id__": 151}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.143, -32.044, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b7p/Ktu3BPB6gjoHULZq4G"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 149}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 28, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "7faakbNWBOsJqoelKMhsf6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 147}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "eesvcEnh1BRYEsC64V3sZp", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 148}, "_children": [{"__id__": 153}], "_active": true, "_components": [{"__id__": 156}, {"__id__": 157}], "_prefab": {"__id__": 158}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 221, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.841, -5.779, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "45E5oQjlRD95H3zMJT6i6j"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 512, "_parent": {"__id__": 152}, "_children": [], "_active": true, "_components": [{"__id__": 154}], "_prefab": {"__id__": 155}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-111, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0arJPoWQhJ4JN045rjknUU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 153}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a072c17b-3759-48c1-861b-2b15a05f697f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "aaA6d4W0VEAI636wY4aYYr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 147}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "01Jrx0A3dGm7qhs0zzxMPI", "sync": false}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 152}, "_enabled": true, "_N$totalLength": 0, "_N$barSprite": {"__id__": 154}, "_N$mode": 0, "_N$progress": 0, "_N$reverse": false, "_id": "5cd+s78vxKVpNQkL10iLJ/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 152}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee9cd1d3-7f34-46bc-a339-55cccd383e08"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "18F7wbWeVH6bC9FnmDZTAm"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 147}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "6aKAke7rtM/7vkkg0WFx/q", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 147}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "adIYsnvxNGdJZZ05q4J1uY", "sync": false}, {"__type__": "66fcdQxu0dEU6jSa0U20wrG", "_name": "", "_objFlags": 0, "node": {"__id__": 147}, "_enabled": true, "progressBar": {"__id__": 156}, "barText": {"__id__": 150}, "loadingPanel": {"__id__": 148}, "_id": "f7bgGLmSZEE5OoH4K+AwSx"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 147}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "1184C1lIZDf6jeqRBWfYo4", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 128}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$target": {"__id__": 129}, "_id": "fc43TFt/xKU49wWdlbuGmI"}, {"__type__": "aea7epIr11IRpWxOhq9G706", "_name": "", "_objFlags": 0, "node": {"__id__": 128}, "_enabled": true, "gameName": "HumanBoy", "gameId": 30, "bundle": "humanboy", "gameNameLabel": {"__id__": 131}, "needDownloadSprite": {"__id__": 138}, "downloadFailSprite": {"__id__": 141}, "cannotDownloadSprite": {"__id__": 144}, "bundleDownloadControl": {"__id__": 164}, "_id": "b7KH71V8lERabi5GGYFFcS"}, {"__type__": "10f659LsiJCs6X3ohKgNi/h", "_name": "", "_objFlags": 0, "node": {"__id__": 128}, "_enabled": true, "progressBar": {"__id__": 160}, "_id": "e64wK3tUZKmKU1em4xqRmz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 128}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "9fVxo0kUJKgaQpH23fVeFp", "sync": false}, {"__type__": "cc.Node", "_name": "GameListItemPokerMaster", "_objFlags": 0, "_parent": {"__id__": 89}, "_children": [{"__id__": 167}, {"__id__": 174}, {"__id__": 185}], "_active": true, "_components": [{"__id__": 200}, {"__id__": 201}, {"__id__": 202}], "_prefab": {"__id__": 203}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-129, 140, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "0f9FCr/GtN9LjsE7nd9ZAH"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 166}, "_children": [{"__id__": 168}], "_active": true, "_components": [{"__id__": 171}, {"__id__": 172}], "_prefab": {"__id__": 173}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 250, "height": 140}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "8c/KQP9/FCKJrtmEbTEwIU"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 167}, "_children": [], "_active": true, "_components": [{"__id__": 169}], "_prefab": {"__id__": 170}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 230, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "fejwMZaXpHapcNbzeVXytD"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 168}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "Cowboy", "_N$string": "Cowboy", "_fontSize": 30, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "8bYuYxzWBAmZDTyTQVPm2I"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 166}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "6cy8tJmYpM1JzlIJwywTA5", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 167}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "1cvWVSnqdKbKfCJ5cXFkCL"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 167}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "c1LtNd/slAYaXmLvRxHM9A"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 166}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "325xvnnZdIzL9nKEDKkUqA", "sync": false}, {"__type__": "cc.Node", "_name": "DownloadIcons", "_objFlags": 0, "_parent": {"__id__": 166}, "_children": [{"__id__": 175}, {"__id__": 178}, {"__id__": 181}], "_active": true, "_components": [], "_prefab": {"__id__": 184}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-87.482, 36.313, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b1gfLmHa5OELLhw4V3lxwL"}, {"__type__": "cc.Node", "_name": "cloud-download", "_objFlags": 0, "_parent": {"__id__": 174}, "_children": [], "_active": false, "_components": [{"__id__": 176}], "_prefab": {"__id__": 177}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 60}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "55Z4qe0mJNXrzrE7qVEzRQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 175}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6dffcfc8-793a-42b3-9c50-090009c95ce8"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2fUYChmMNLv7DFwOCRSbuA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 166}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "fef9N/oBVAFrdNtKrqLZbq", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-download-fail", "_objFlags": 0, "_parent": {"__id__": 174}, "_children": [], "_active": false, "_components": [{"__id__": 179}], "_prefab": {"__id__": 180}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 54, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "e85WBGQRpEGLs7UxTLaCqx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 178}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "485a034b-67c9-48f6-90c0-12dc40cc0973"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "e3bXHNAK1ND44Bq4IraEOs"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 166}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "eaoXhSeMhLBZ2gunzdLkov", "sync": false}, {"__type__": "cc.Node", "_name": "cloud-disconnected", "_objFlags": 0, "_parent": {"__id__": 174}, "_children": [], "_active": false, "_components": [{"__id__": 182}], "_prefab": {"__id__": 183}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 56}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "bcDAlbcq9CPYrHeNbU3Tbw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 181}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b63eb81d-9677-4731-95ee-a16810ef8d93"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "6dDGK+I3tN36TCdC6Fw2KD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 166}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "a0aYrLhM1PN63JYbRX1G5s", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 166}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "31ml3l7SNIN7U5kk70tTmC", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 166}, "_children": [{"__id__": 186}], "_active": false, "_components": [{"__id__": 198}], "_prefab": {"__id__": 199}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "23jD61IOFFybc322hyWqzV"}, {"__type__": "cc.Node", "_name": "panel", "_objFlags": 0, "_parent": {"__id__": 185}, "_children": [{"__id__": 187}, {"__id__": 190}], "_active": true, "_components": [], "_prefab": {"__id__": 197}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "2eiLUzO6tMiLoxUWXhusO7"}, {"__type__": "cc.Node", "_name": "barText", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [], "_active": true, "_components": [{"__id__": 188}], "_prefab": {"__id__": 189}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 37.8}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [3.143, -32.044, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "47qRYUGURJ+ojBRvkVx2GP"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 187}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "", "_N$string": "", "_fontSize": 28, "_lineHeight": 30, "_enableWrapText": true, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": "53O462f8ZBQYP2R/pP/KAO"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 185}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "eesvcEnh1BRYEsC64V3sZp", "sync": false}, {"__type__": "cc.Node", "_name": "ProgressBar", "_objFlags": 0, "_parent": {"__id__": 186}, "_children": [{"__id__": 191}], "_active": true, "_components": [{"__id__": 194}, {"__id__": 195}], "_prefab": {"__id__": 196}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 221, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.841, -5.779, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "44j6ZDJndFe6fxolvzQssY"}, {"__type__": "cc.Node", "_name": "bar", "_objFlags": 512, "_parent": {"__id__": 190}, "_children": [], "_active": true, "_components": [{"__id__": 192}], "_prefab": {"__id__": 193}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 20}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-111, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "64Zg0pPPxBo7PBKxIc2Hiy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 191}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a072c17b-3759-48c1-861b-2b15a05f697f"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "45HmXsT7RNYKC+UsiLI7Wc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 185}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "01Jrx0A3dGm7qhs0zzxMPI", "sync": false}, {"__type__": "cc.ProgressBar", "_name": "", "_objFlags": 0, "node": {"__id__": 190}, "_enabled": true, "_N$totalLength": 0, "_N$barSprite": {"__id__": 192}, "_N$mode": 0, "_N$progress": 0, "_N$reverse": false, "_id": "8dNcRRnfZOcZf7ceAGCvl7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 190}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "ee9cd1d3-7f34-46bc-a339-55cccd383e08"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "049kuMRDpP9rcW7vy3xuSB"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 185}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "6aKAke7rtM/7vkkg0WFx/q", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 185}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "adIYsnvxNGdJZZ05q4J1uY", "sync": false}, {"__type__": "66fcdQxu0dEU6jSa0U20wrG", "_name": "", "_objFlags": 0, "node": {"__id__": 185}, "_enabled": true, "progressBar": {"__id__": 194}, "barText": {"__id__": 188}, "loadingPanel": {"__id__": 186}, "_id": "a3Zo+J5zZND6UKVgot+FZq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 185}, "asset": {"__uuid__": "94a6d10d-cbd1-42e0-8ee7-4f2c4e875746"}, "fileId": "1184C1lIZDf6jeqRBWfYo4", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 166}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$target": {"__id__": 167}, "_id": "44JfeVwaJGv49vg3RfjIYl"}, {"__type__": "aea7epIr11IRpWxOhq9G706", "_name": "", "_objFlags": 0, "node": {"__id__": 166}, "_enabled": true, "gameName": "PokerMaster", "gameId": 70, "bundle": "poker-master", "gameNameLabel": {"__id__": 169}, "needDownloadSprite": {"__id__": 176}, "downloadFailSprite": {"__id__": 179}, "cannotDownloadSprite": {"__id__": 182}, "bundleDownloadControl": {"__id__": 202}, "_id": "733Il7At5B66HOeDaDEuw/"}, {"__type__": "10f659LsiJCs6X3ohKgNi/h", "_name": "", "_objFlags": 0, "node": {"__id__": 166}, "_enabled": true, "progressBar": {"__id__": 198}, "_id": "bb/UeRi0xEw5pq5B8z0wjy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 166}, "asset": {"__uuid__": "6964bd4c-8a03-439b-ae3a-a4d1e5b76f4f"}, "fileId": "73VHJV/0BOWr/yNdc4QLXg", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 89}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9bbda31e-ad49-43c9-aaf2-f7d9896bac69"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "33zE2A2VJMAJImqaUT/hA6"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 89}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 568, "height": 780}, "_resize": 0, "_N$layoutType": 3, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 30, "_N$paddingRight": 0, "_N$paddingTop": 20, "_N$paddingBottom": 0, "_N$spacingX": 10, "_N$spacingY": 20, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "edWCYTAjlCg73mqbw0MYWG"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 89}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 200, "_originalHeight": 150, "_id": "edaMRsfEtAuYf6F9vrSPDC"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 77}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "d4msB0q4tNsaLOlKDei1ag", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 84}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 220, "_originalHeight": 400, "_id": "dadOpCR99IvqW7NXlr8GPW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 77}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "4cBfOJ6lJDdrYiZ8HvCnKb", "sync": false}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 37, "_left": 350.07654921020657, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 237, "_id": "46Kaq/6wVNoJnPKr17jW7R"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "5fe5dcaa-b513-4dc5-a166-573627b3a159"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "c0AsvAyztIiZ9ol20pGcC7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 77}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "72rkSqC7ZKWZZWzT+YfSKl", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 77}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9bbda31e-ad49-43c9-aaf2-f7d9896bac69"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "22rCRWyupDHbCuFFgX8cjW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 77}, "asset": {"__uuid__": "f6b87b1c-18fe-4ff6-a0cd-198ffc148faa"}, "fileId": "3dZYbaMMtBfb26Au7/Dokj", "sync": false}, {"__type__": "cc.Node", "_name": "LanguageSettingButton", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 216}], "_active": true, "_components": [{"__id__": 223}], "_prefab": {"__id__": 227}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [222.095, 413.962, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "9bXicKz3lNwYhULWm0shkb"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 215}, "_children": [{"__id__": 217}], "_active": true, "_components": [{"__id__": 220}, {"__id__": 221}], "_prefab": {"__id__": 222}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "faqe5a3uxGC4DwKGMO6HbN"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 216}, "_children": [], "_active": true, "_components": [{"__id__": 218}], "_prefab": {"__id__": 219}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "47caMUgwxDkLyigp/qPM1b"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 217}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "語言", "_N$string": "語言", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "92w3zRsr1A3Lg5T0QLZo+H"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "87++rKoV9KkJe20Nl38SlN", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "761hxU3u5BGZsT47jgxzhS"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 216}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "107M5aFNxNkY73jOsawKtF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "a83UFg49pKDI2IQT70utbU", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 215}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 224}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 216}, "_id": "4225SLB6FHZac/FlQYWq2o"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 225}, "component": "", "_componentId": "244f9nlJENGzI4gsBJoi3Ke", "handler": "show", "customEventData": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 226}, "_name": "LanguageSetting", "_active": false, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "16aqyZw5tK0a1mjlU5DnXp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 225}, "asset": {"__uuid__": "6444dfe7-2f71-48ab-ba30-93e16f32399e"}, "fileId": "27CZnL139DdZaO5JdeT4VQ", "sync": true}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "655gHORS5PbZFW8pxV0lGl", "sync": false}, {"__type__": "cc.Node", "_name": "RoomListView", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 229}], "_active": false, "_components": [{"__id__": 244}], "_prefab": {"__id__": 245}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -94.795, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "14pPxLnINE56nasZr18LTu"}, {"__type__": "cc.Node", "_name": "RoomListPanel", "_objFlags": 0, "_parent": {"__id__": 228}, "_children": [{"__id__": 230}], "_active": true, "_components": [{"__id__": 241}, {"__id__": 242}], "_prefab": {"__id__": 243}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -11.460000000000036, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "aeu62OPWBP0pJMgLZaKA7c"}, {"__type__": "cc.Node", "_name": "ScrollView", "_objFlags": 0, "_parent": {"__id__": 229}, "_children": [{"__id__": 231}], "_active": true, "_components": [{"__id__": 237}, {"__id__": 238}, {"__id__": 239}], "_prefab": {"__id__": 240}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 580, "height": 900}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-5.725999999999999, 90, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6dO8J+3aNL1pQars41pb4U"}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 512, "_parent": {"__id__": 230}, "_children": [{"__id__": 232}], "_active": true, "_components": [{"__id__": 235}], "_prefab": {"__id__": 236}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 360, "height": 500}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cd8m5xNu5HL6Sr2PHFmszF"}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 512, "_parent": {"__id__": 231}, "_children": [], "_active": true, "_components": [{"__id__": 233}], "_prefab": {"__id__": 234}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 180, "height": 216}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 115.30999755859375, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "b11i5IGx9IeoKVaqHADeZq"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 232}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 180, "height": 216}, "_resize": 1, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 20, "_N$spacingX": 0, "_N$spacingY": 30, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "c1uoKk2/lMvbJ8unNkEAS0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 228}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "98Mdwzj5xPmZV09zws4eBr", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 231}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0, "_N$inverted": false, "_id": "e5vxXi1j5EQ7IXjopPZsVt"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 228}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "d1zTjQQfROT7mkp8JSekXt", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 230}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9bbda31e-ad49-43c9-aaf2-f7d9896bac69"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "65+L64b9pH5phTtpFVFqpC"}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 230}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 232}, "content": {"__id__": 232}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": "e6Y0w7zD5Nn4tSto/8PwzN"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 230}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 45, "_left": -145.72599999999997, "_right": -134.27400000000003, "_top": 5.684341886080802e-14, "_bottom": 180, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 240, "_originalHeight": 250, "_id": "4arCBD/uVPPLWoftbsAkCj"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 228}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "37MAvaLv5Jj4mT2dCoFARj", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 229}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 300, "height": 1080}, "_resize": 0, "_N$layoutType": 2, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": "99WL139TxKQptALamRKrD6"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 229}, "_enabled": true, "alignMode": 1, "_target": null, "_alignFlags": 5, "_left": 810, "_right": 810, "_top": 11.460000000000047, "_bottom": -11.460000000000047, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 300, "_originalHeight": 200, "_id": "12rb+wsXVHaZGX3e+c0Les"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 228}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "4clgvRfx1IhaHw3pFTmAni", "sync": false}, {"__type__": "62c118m6DBL1agLPbw0bMd1", "_name": "", "_objFlags": 0, "node": {"__id__": 228}, "_enabled": true, "scrollView": {"__id__": 238}, "roomListItemPrefab": {"__uuid__": "8ef5863e-b798-472a-970d-855d7bd95ea1"}, "_id": "c9YH+Y+yVKEL0dI6FR1DPp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 228}, "asset": {"__uuid__": "6af19f94-dd64-4a22-94dd-3f1a4530819e"}, "fileId": "4fFFA7Wk1L35JexYlf9XOt", "sync": false}, {"__type__": "cc.Node", "_name": "ShieldButton", "_objFlags": 0, "_parent": {"__id__": 72}, "_children": [{"__id__": 247}], "_active": false, "_components": [{"__id__": 254}], "_prefab": {"__id__": 255}, "_opacity": 0, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "6dhsefIA1F66hsOKPwoc3e"}, {"__type__": "cc.Node", "_name": "Background", "_objFlags": 512, "_parent": {"__id__": 246}, "_children": [{"__id__": 248}], "_active": true, "_components": [{"__id__": 251}, {"__id__": 252}], "_prefab": {"__id__": 253}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 1920, "height": 1080}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "cfLTHf/B9GwrrUmVzFgNZo"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 512, "_parent": {"__id__": 247}, "_children": [], "_active": true, "_components": [{"__id__": 249}], "_prefab": {"__id__": 250}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 40}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": "dflggibh9AsZzaMvsv1efc"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 248}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_string": "button", "_N$string": "button", "_fontSize": 20, "_lineHeight": 40, "_enableWrapText": false, "_N$file": null, "_isSystemFontUsed": true, "_spacingX": 0, "_batchAsBitmap": false, "_styleFlags": 0, "_underlineHeight": 0, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 1, "_id": "43K8aadJ1K86rL1ZV3IOn0"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "22P7mP6RVLMpHJJ/vn/GM4", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": "2dwpS1fRJCw6gRPAqofrE1"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "node": {"__id__": 247}, "_enabled": true, "alignMode": 0, "_target": null, "_alignFlags": 45, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_verticalCenter": 0, "_horizontalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 40, "_id": "a936XXw9xGC4R5mC6qvsRY"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "cd90IcfjRETpUEph0p7Cnb", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 246}, "_enabled": true, "_normalMaterial": null, "_grayMaterial": null, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 230, "g": 230, "b": 230, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 120, "g": 120, "b": 120, "a": 200}, "_N$normalSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "pressedSprite": {"__uuid__": "e9ec654c-97a2-4787-9325-e6a10375219a"}, "_N$hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "hoverSprite": {"__uuid__": "f0048c10-f03e-4c97-b9d3-3506e1d58952"}, "_N$disabledSprite": {"__uuid__": "29158224-f8dd-4661-a796-1ffab537140e"}, "_N$target": {"__id__": 247}, "_id": "93YJTtR/BGYKEeojX90tu+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "aa7hQeXkVI06vQ/efI+7Ws", "sync": false}, {"__type__": "c1f54m7435HtLfNeaijuCfD", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "roomListControl": {"__id__": 244}, "_id": "f4xoF1A9FOGqkmE88ICSDW"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "69qWsmS9FH653cy06iAAEv", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "deWZ4EiVFFTZ3xYPreHAB+", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "edduYMEoxEz7IVP9PQmywe", "sync": false}, {"__type__": "fbfc110zNpLv68CPa39Whm6", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "isMockSocket": false, "isLoadFeatureTestBundle": false, "_id": "a5n5C3fv5GWoUKEczTd//r"}, {"__type__": "d4a47U2kGxJsr6bc/k01Ofa", "_name": "", "_objFlags": 0, "node": {"__id__": 7}, "_enabled": true, "loginPanel": {"__id__": 12}, "miniGamePanel": {"__id__": 72}, "_id": "39mNcPA0NIloyAU9V29oRl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 7}, "asset": {"__uuid__": "f6c3665b-0253-43c0-9172-2d56e2d2a3da"}, "fileId": "", "sync": false}]