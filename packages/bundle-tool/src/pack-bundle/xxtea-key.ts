import { Project } from '../type';
import ConfigManager from './config-manager';

export default class XxteaKey {
    // 保留默认密钥作为后备方案
    private static defaultKeyMapping: Record<Project, string> = {
        [Project.PKW]: '05279ccb-8728-4a',
        [Project.WPK]: 'a3a2836d-0a32-1c'
    };

    /**
     * 获取项目的XXTEA加密密钥
     * 优先从配置文件读取，如果没有则使用默认值
     */
    static get(project: Project): string {
        try {
            // 尝试从配置文件获取密钥
            const configKey = ConfigManager.getXxteaKey(project);
            if (configKey) {
                return configKey;
            }
        } catch (error) {
            Editor.warn(`[XxteaKey] Failed to get key from config for project ${project}:`, error);
        }

        // 如果配置文件中没有，使用默认密钥
        const defaultKey = this.defaultKeyMapping[project];
        if (defaultKey) {
            Editor.info(`[XxteaKey] Using default key for project ${project}`);
            return defaultKey;
        }

        // 如果都没有，抛出错误
        throw new Error(`No XXTEA key found for project: ${project}`);
    }

    /**
     * 检查项目是否有配置的密钥
     */
    static hasConfiguredKey(project: Project): boolean {
        try {
            const configKey = ConfigManager.getXxteaKey(project);
            return !!configKey;
        } catch (error) {
            return false;
        }
    }
}
