import {
    type IEnv,
    type IPackedBundle,
    type Project,
    DevicePlatform,
    CocosNativeBuildTemplate,
    BuiltPlatformDirectoryName
} from '../type';
import type { ExecException } from 'child_process';
import { URL } from 'url';
import { spawn } from 'child_process';
import { exec } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import XxteaKey from './xxtea-key';

export function getBundleArray(sourceData: any) {
    return Object.keys(sourceData.bundles).map((key) => {
        const bundle = sourceData.bundles[key];
        return { key, ...bundle, included: bundle.included };
    });
}
// NOTE: get url from bundleServerAddress, remove url path and query.
export function getUpdateURL(bundleServerAddress: string): string {
    try {
        const url = new URL(bundleServerAddress);
        return url.href;
    } catch (error) {
        throw new Error('Invalid bundleServerAddress: ' + bundleServerAddress);
    }
}

export function fetchBundleSuffixMD5(dir: string, callback: (md5: string | undefined) => void): string | undefined {
    let stat = fs.statSync(dir);
    if (!stat.isDirectory()) {
        return;
    }
    let subpaths = fs.readdirSync(dir).filter((subpath: string) => subpath.startsWith('config.'));
    for (let subpathItem of subpaths) {
        if (subpathItem[0] === '.') {
            continue;
        }
        const md5 = subpathItem.split('.')[1];
        callback(md5);
        return md5 || undefined;
    }
}
export function readJsonFile(jsonPath: string): any | null {
    if (fs.existsSync(jsonPath)) {
        let data = fs.readFileSync(jsonPath, 'utf-8');
        if (!isJSON(data)) {
            fs.unlinkSync(jsonPath);
            return null;
        }
        let source = JSON.parse(data);
        return source;
    } else {
        // Editor.log('JSON file not exist:' + jsonPath);
        return null;
    }
}
export function writeJSONFile(filePath: string, jsonData: string): void {
    fs.writeFileSync(filePath, jsonData);
}
/**
 * Clean target directory and httpserver public directory before pack operation
 */
export function cleanPackDirectories(targetPath: string, projectPath: string): void {
    // Clean target directory
    if (fs.existsSync(targetPath)) {
        fs.rmSync(targetPath, { recursive: true, force: true });
        Editor.info(`[bundle-tool] Cleaned target directory: ${targetPath}`);
    }

    // Clean httpserver public directory
    const httpServerPublicPath = path.join(projectPath, 'packages', 'bundle-tool', 'httpserver', 'public');
    if (fs.existsSync(httpServerPublicPath)) {
        fs.rmSync(httpServerPublicPath, { recursive: true, force: true });
        Editor.info(`[bundle-tool] Cleaned httpserver public directory: ${httpServerPublicPath}`);
    }

    // Recreate directories
    fs.mkdirSync(targetPath, { recursive: true });
    fs.mkdirSync(httpServerPublicPath, { recursive: true });
}

export function copyBundle(
    sourcePath: string,
    targetPath: string,
    bundles: Record<string, IPackedBundle>,
    callback: (info: string) => void
): void {
    if (!fs.existsSync(targetPath)) {
        fs.mkdirSync(targetPath, { recursive: true });
    } else {
        fs.rmSync(targetPath, { recursive: true, force: true });
        fs.mkdirSync(targetPath, { recursive: true });
    }
    for (const bundleKey in bundles) {
        const bundlePath = path.join(sourcePath, bundleKey);
        const exportPath = path.join(targetPath, bundleKey);
        copyDirectory(bundlePath, exportPath);
    }
    callback('Bundles copied successfully.');
}

function copyDirectory(source: string, target: string): void {
    if (!fs.existsSync(target)) {
        fs.mkdirSync(target, { recursive: true });
    }
    const files = fs.readdirSync(source);
    for (let file of files) {
        const sourceFile = path.join(source, file);
        const targetFile = path.join(target, file);
        if (fs.statSync(sourceFile).isDirectory()) {
            copyDirectory(sourceFile, targetFile);
        } else {
            fs.copyFileSync(sourceFile, targetFile);
        }
    }
}

function isJSON(str: string): boolean {
    try {
        JSON.parse(str);
        return true;
    } catch (error) {
        // Editor.log('JSON file is invalid');
        return false;
    }
}

export function getSourceEnv(env: IEnv) {
    // TODO: refactor
    const Console = env.console;

    const dotEnvPath = env.context.cocosCreator.EnvVariablesJSONPath;

    // NOTE: check if file exist
    if (!fs.existsSync(dotEnvPath)) {
        // eslint-disable-next-line autofix/quotes
        Console.error("temp env_variables.json didn't exist");
        return;
    }
    return fs.readFileSync(dotEnvPath, { encoding: 'utf8' });
}

export function covertExportDevicePlatformToBundlePlatform(platformName: string, template: CocosNativeBuildTemplate) {
    const isWebMobilePlatform = platformName === DevicePlatform.WEB_MOBILE;
    if (isWebMobilePlatform) {
        return BuiltPlatformDirectoryName.WEB_MOBILE;
    }

    const isJSBLinkTemplate = template === CocosNativeBuildTemplate.LINK;
    if (isJSBLinkTemplate) {
        return BuiltPlatformDirectoryName.JSB_LINK;
    } else {
        return BuiltPlatformDirectoryName.JSB_DEFAULT;
    }
}

export function buildProject(
    env: IEnv,
    directory: string,
    project: Project,
    platform: DevicePlatform,
    buildTemplateOption: CocosNativeBuildTemplate,
    callback: (info: string, error: boolean) => void
): void {
    const Console = env.console;

    // NOTE: get env config
    const config = JSON.parse(getSourceEnv(env) as string);
    if (!config) {
        const message = 'env_variables not exist';
        Console.error(message);
        callback(message, true);
        // return;
    }

    // NOTE: get cocos creator path
    const cocosCreatorPath = config.cocosCreatorPath;
    if (!fs.existsSync(cocosCreatorPath)) {
        const message = `Cocos Creator not exist：${cocosCreatorPath}`;
        Console.error(message);
        callback(message, true);
        return;
    }

    // TODO:
    // NOTE: get build params
    // const md5CacheFlag = platform === DevicePlatformEnum['web-mobile'] ? String(true) : String(false);
    // const inlineSpriteFrameFlag = platform === DevicePlatformEnum['web-mobile'] ? String(true) : String(false);
    const md5CacheFlag = platform === DevicePlatform.WEB_MOBILE ? String(true) : String(false);
    const inlineSpriteFrameFlag = platform === DevicePlatform.WEB_MOBILE ? String(true) : String(false);

    // TODO: didn't need to a7c580ac-7944-4778-b22a-49c8b46a9195
    let buildString = `
    platform=${platform};
    md5Cache=${md5CacheFlag};
    debug=false;
    inlineSpriteFrame=${inlineSpriteFrameFlag};
    mergeStartScene=false;
    optimizeHotUpdate=false;
    encryptJs=true;
    zipCompressJs=true;       
    orientation={'portrait':true,'upsideDown':false,'landscapeLeft':true,'landscapeRight':true};
  `;

    // Editor.log('config', config);

    if (platform === DevicePlatform.ANDROID || platform === DevicePlatform.IOS) {
        // NOTE:xxteakey
        const xxteakey = XxteaKey.get(project);
        // if(typeof xxteakey === 'undefined') {
        //   callback('The xxteakey of .env didn\'t not exist. please setting it in .env and restart cocos creator', true);
        //   return;
        // } else if(xxteakey === '') {
        //   callback('The xxteakey of .env is empty. please setting it in .env.', true);
        // }
        buildString += `
      template=${buildTemplateOption};
      xxteaKey=${xxteakey};
    `;
    }

    Editor && Editor.info('[build-tool] cocos creator project ', project);
    Editor && Editor.info('[build-tool] cocos creator buildString ', buildString);

    // NOTE: get android build params
    if (platform === DevicePlatform.ANDROID) {
        buildString += `
      apiLevel=android-30;      
      packageName=${config.packageNameAndroid};      
      keystorePath=${config.keystorePath};
      keystorePassword=${config.keystorePassword};
      keystoreAlias=${config.keystoreAlias};
      keystoreAliasPassword=${config.keystoreAliasPassword}
      useDebugKeystore=false;     
    `;
    }
    buildString = buildString.replace(/\s+/g, '');
    buildString = buildString.split('\n').join('').trim();

    Console.info('[build-tool] cocos creator - build params');
    buildString.split(';').forEach((params) => {
        Console.info(params.replace('=', ' : '));
    });
    const cmd = `${config.cocosCreatorPath} --path ${env.context.cocosCreator.ProjectPath} --build "${buildString}"`;

    // NOTE: get build command
    Console.info('[build-tool] cocos creator - build command');
    Console.info(cmd);

    execCommand(env, cmd, directory, callback);
}

function execCommand(env: IEnv, command: string, cwd: string, callback: (info: string, error: boolean) => void): void {
    const Console = env.console;

    const options = {
        cwd
    };
    exec(command, options, (err: ExecException | null, stdout: string, stderr: string) => {
        let result = '';
        Console.log('stdout:', stdout);
        Console.log('exec.stderr:', stderr);
        // NOTICE: ignore error history: exec.stderr: Load profile failed: local://settings.json
        // if (stderr) {
        //   result = 'command finish, please check the console for details';
        //   callback(result, true);
        // } else {
        result = 'command succeeded';
        callback(result, false);
        // }
    });
}

export function getDirectories(srcPath: string) {
    if (!fs.existsSync(srcPath)) {
        // Editor.log('didn\'t exist: ', srcPath);
        return [];
    }
    return fs.readdirSync(srcPath).filter((file: string) => {
        if (['internal', 'main', 'resources'].indexOf(file) > -1) {
            return false;
        }
        return fs.statSync(path.join(srcPath, file)).isDirectory();
    });
}

export function generateVersionManifestFile(
    { sourceData, Context, platformName, buildTemplateOption, exportPath }: any,
    ignoreBundleNameArray: string[],
    callback: () => void
) {
    // 新增: 支持 bundleUrls 参数
    // 修改: bundleUrls 更名为 bundleInfoMap
    const bundleInfoMap: Record<string, { url: string, version?: string, dependencies?: string[] }> = {};
    if (sourceData.bundles) {
        Object.keys(sourceData.bundles).forEach((key) => {
            const bundle = sourceData.bundles[key];
            if (bundle.url) {
                bundleInfoMap[key] = { url: bundle.url };
                if (bundle.version) {
                    bundleInfoMap[key].version = bundle.version;
                }
                if (Array.isArray(bundle.dependencies) && bundle.dependencies.length > 0) {
                    bundleInfoMap[key].dependencies = bundle.dependencies;
                }
            }
        });
    }
    const args = [
        '-v',
        sourceData.version,
        '-u',
        getUpdateURL(sourceData.bundleServerAddress),
        '-s',
        Context.cocosCreator.getBuiltPlatformPath(
            covertExportDevicePlatformToBundlePlatform(platformName, buildTemplateOption)
        ),
        '-d',
        exportPath,
        '-g',
        ignoreBundleNameArray.join(',')
    ];
    // 兼容性: 只有有 bundleInfoMap 时才加参数
    if (Object.keys(bundleInfoMap).length > 0) {
        args.push('-b', JSON.stringify(bundleInfoMap));
    }
    Editor.info('[bundle-tool] bundleInfoMap' + JSON.stringify(bundleInfoMap));

    const script = Context.bundleTool.generateVersionManifest.ScriptPath;
    const child = spawn('node', [script, ...args]);
    child.stdout.on('data', (data: any) => {});
    child.stderr.on('data', (data: any) => {});
    child.on('close', (code: any) => {
        callback();
    });
}

/**
 * 生成主包 manifest 文件
 */
export function generateMainPackageManifestFile(
    { mainBundleData, Context, platformName, buildTemplateOption, exportPath, selectedProject }: any,
    callback: () => void
) {
    const args = [
        '-v',
        mainBundleData.version,
        '-u',
        getUpdateURL(mainBundleData.bundleServerAddress),
        '-s',
        Context.cocosCreator.getBuiltPlatformPath(
            covertExportDevicePlatformToBundlePlatform(platformName, buildTemplateOption)
        ),
        '-d',
        exportPath
    ];

    // 添加排除的 bundle 列表参数（优先使用 UI 状态）
    if (selectedProject) {
        try {
            const includedBundles: string[] = [];

            const cacheJsonPath = path.join(Context.cocosCreator.ProjectPath, 'packages', 'bundle-tool', 'cache.json');
            // Editor.info(`[bundle-tool] Looking for cache.json at: ${cacheJsonPath}`);
            const cacheJsonContent = fs.readFileSync(cacheJsonPath, 'utf-8');
            const cacheJson = JSON.parse(cacheJsonContent);
            // Editor.info(`[bundle-tool] cacheJsonContent: ${cacheJsonContent}`);
            // Editor.info(`[bundle-tool] cacheJson: ${cacheJson.sourceData.bundles}`);
            if (cacheJson && cacheJson.sourceData.bundles) {
                for (const [key, bundle] of Object.entries(cacheJson.sourceData.bundles)) {
                    const b = bundle as { included?: boolean };
                    Editor.info(`[bundle-tool] key: ${key} included: ${b.included}`);
                    if (b && b.included === true) {
                        includedBundles.push(key);
                    }
                }
            }
            if (includedBundles.length > 0) {
                args.push('-ib', JSON.stringify(includedBundles));
                Editor.info('[bundle-tool] Excluding bundles from main package manifest:', includedBundles);
            } else {
                Editor.info('[bundle-tool] No bundles excluded from main package manifest');
            }
        } catch (error) {
            Editor.error('[bundle-tool] Failed to get bundle configs for main package:', error);
        }
    }

    Editor.info('[bundle-tool] Generating main package manifest with args:', args);

    const script = path.join(Context.cocosCreator.ProjectPath, 'packages', 'bundle-tool', 'tools', 'main_package_generator.js');
    const child = spawn('node', [script, ...args]);

    child.stdout.on('data', (data: any) => {
        Editor.info('[bundle-tool] main package generator stdout:', data.toString());
    });

    child.stderr.on('data', (data: any) => {
        Editor.error('[bundle-tool] main package generator stderr:', data.toString());
    });

    child.on('close', (code: any) => {
        Editor.info('[bundle-tool] main package generator finished with code:', code);
        callback();
    });
}
