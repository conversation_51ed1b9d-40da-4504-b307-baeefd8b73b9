import type {
    Environment,
    IBundleSourceData,
    ICacheBundle,
    IVueBundle,
    IVueData,
    PartialRecord,
    Project,
    IMainPackageConfig,
    IVueDataExtended
} from '../type';
import { type CocosNativeBuildTemplate, type ICacheSourceData, type ICacheFileObject, DevicePlatform, UpdateType } from '../type';
import { getDirectories, buildProject, getSourceEnv, getBundleArray, generateVersionManifestFile, generateMainPackageManifestFile } from './helper';
import {
    copyBundle,
    covertExportDevicePlatformToBundlePlatform,
    fetchBundleSuffixMD5,
    readJsonFile,
    writeJSONFile,
    cleanPackDirectories
} from './helper';

import { EditorConsole } from '../type';
import { ContextUI } from './context-ui';
import * as path from 'path';
import * as os from 'os';
import * as fs from 'fs';
import ContextConfig from './context';

import ConfigManager from './config-manager';

const Electron = require('electron');
const Context = ContextConfig.get(Editor.Project.path);

declare global {
    interface Window {
        Vue: any;
    }
}

class BundleToolContext {
    project: Project;
    environment: Environment;
    platformName: DevicePlatform;
    buildTemplateOption: CocosNativeBuildTemplate;
    sourcePath: string;
    exportPath: string;
    sourceData: ICacheSourceData;
    bundleArray: IVueBundle[];
    isProcessing: boolean;

    constructor() {
        const context = ContextConfig.get(Editor.Project.path);
        this.project = context.bundleTool.build.DefaultProjectOption as Project;
        this.environment = context.bundleTool.build.DefaultEnvironmentOption as Environment;
        this.platformName = context.bundleTool.build.DefaultPlatformOption as DevicePlatform;
        this.buildTemplateOption = context.cocosCreator.DefaultTemplateOption as CocosNativeBuildTemplate;
        this.sourcePath = context.bundleTool.packConfiguration.getSourcePath(
            covertExportDevicePlatformToBundlePlatform(this.platformName, this.buildTemplateOption)
        );
        this.exportPath = context.bundleTool.packConfiguration.DefaultExportPath;
        this.sourceData = context.bundleTool.pack.DefaultSourceData;
        this.bundleArray = getBundleArray(this.sourceData);
        this.isProcessing = false;
        this.sourceData.bundles = {};
    }
}

class ExtensionPanel {
    private static isLock = false;
    private context: BundleToolContext;

    constructor(context: BundleToolContext) {
        this.context = context;
    }

    loadCacheFile(): void {
        if (ExtensionPanel.isLock) {
            Editor.error('[bundle-tool] loadCacheFile() isLock, cannot loadCacheFile');
            return;
        }
        ExtensionPanel.isLock = true;

        const filePath = ContextUI.bundleTool.cache.CacheJSONPath;
        const cache: ICacheFileObject = readJsonFile(filePath);
        if (cache === null) {
            Editor.info('[bundle-tool] loadEditProgress() is null, using default values.');
        } else {
            this.updateContextFromCache(cache);
        }

        ExtensionPanel.isLock = false;
    }

    private updateContextFromCache(cache: ICacheFileObject) {
        // Editor.info('[bundle-tool] updateContextFromCache', JSON.stringify(cache.sourceData.bundles));
        for (const key in cache.sourceData.bundles) {
            const cachedSourceDataBundle = cache.sourceData.bundles[key];
            const existingBundleIndex = this.context.bundleArray.findIndex((bundle) => bundle.key === key);
            if (existingBundleIndex !== -1) {
                this.context.bundleArray[existingBundleIndex] = {
                    key,
                    ...cachedSourceDataBundle,
                    included: cachedSourceDataBundle.included
                };
            }
        }
        this.context.platformName = cache.platformName;
        this.context.buildTemplateOption = cache.buildTemplateOption;
        this.context.sourcePath = cache.sourcePath;
        this.context.exportPath = cache.exportPath;
        this.context.sourceData = cache.sourceData;
        this.context.bundleArray = Object.keys(cache.sourceData.bundles).map((key) => {
            const bundle = this.context.sourceData.bundles[key];
            return {
                key,
                ...bundle,
                version: bundle.version,
                url: bundle.url,
                included: bundle.included,
                dependencies: Array.isArray(bundle.dependencies) ? bundle.dependencies : []
            };
        });
        // Editor.info('[bundle-tool] bundleArray', JSON.stringify(this.context.bundleArray));

        const bundleNameOfDirectoryArray = getDirectories(this.context.sourcePath);
        const cacheBundleKeys = this.context.bundleArray.map((bundle) => bundle.key);
        if (bundleNameOfDirectoryArray.length !== cacheBundleKeys.length) {
            this.context.sourceData.bundles = {};
        }
        this.updateBundles(bundleNameOfDirectoryArray);
    }

    private updateBundles(bundleNameOfDirectoryArray: string[]) {
        const newBundles: Record<string, any> = {};
        const newBundleArray: any[] = [];
        for (const bundleName of bundleNameOfDirectoryArray) {
            const newBundle = this.getNewBundle(this.context.sourcePath, bundleName);
            newBundleArray.push(newBundle);
            newBundles[bundleName] = newBundle;
        }
        this.context.bundleArray = newBundleArray;
        this.context.sourceData.bundles = newBundles;

        // Editor.info('[bundle-tool] updateBundles bundleArray', JSON.stringify(this.context.bundleArray));
    }

    getNewBundle(sourcePath: string, bundleName: string) {
        interface Bundle {
            key: string;
            md5: string;
            version: string | undefined;
            included: boolean;
            url: string;
            dependencies: string[];
        }
        
        let newBundle: Bundle = {
            key: '',
            md5: '',
            version: '',
            included: false,
            url: '',
            dependencies:[]
        };
        const bundlePath = path.join(sourcePath, bundleName);
        fetchBundleSuffixMD5(bundlePath, (md5) => {
            const preBundle = this.context.bundleArray.filter((bundle) => bundle.key === bundleName);
            newBundle = {
                key: bundleName,
                md5: md5 || '',
                version: preBundle[0]?.version,
                included: preBundle && preBundle[0] && preBundle[0].included ? preBundle[0].included : false,
                url: preBundle && preBundle[0] && preBundle[0].url ? preBundle[0].url : '',
                dependencies:preBundle[0]?.dependencies ?? []
            };
        });
        return newBundle;
    }

    syncCacheFile() {
        if (ExtensionPanel.isLock) {
            Editor.error('[bundle-tool] loadCacheFile() isLock, cannot syncCacheFile');
            return;
        }
        ExtensionPanel.isLock = true;
        const cache: ICacheFileObject = {
            platformName: this.context.platformName,
            sourcePath: this.context.sourcePath,
            exportPath: this.context.exportPath,
            sourceData: this.context.sourceData,
            buildTemplateOption: this.context.buildTemplateOption
        };

        const path = ContextUI.bundleTool.cache.CacheJSONPath;
        const str = JSON.stringify(cache, null, 2);
        writeJSONFile(path, str);
        ExtensionPanel.isLock = false;
    }

    fetchBundlesMD5AndRenewBundleArray(callback: (result: IVueBundle[]) => void) {
        const prevBundleArray = [...this.context.bundleArray];
        const currentProject = ConfigManager.getCurrentProject();
        syncBundleListFromConfigToBundleList(prevBundleArray, currentProject || undefined);
        for (const prevBundle of prevBundleArray) {
            const bundlePath = path.join(this.context.sourcePath, prevBundle.key);
            if (!fs.existsSync(bundlePath)) {
                Editor.log(`[bundle-tool] directory not exist : ${bundlePath}`);
                prevBundle.md5 = '';
                prevBundle.included = false;
                delete this.context.sourceData.bundles[prevBundle.key];
                continue;
            }
            fetchBundleSuffixMD5(bundlePath, (md5) => {
                const bundle = this.context.sourceData.bundles[prevBundle.key];
                prevBundle.md5 = md5;
                prevBundle.included = bundle ? bundle.included : false;
            });
        }
        callback(prevBundleArray);
    }

    checkCacheMatchBundlesMD5() {
        this.fetchBundlesMD5AndRenewBundleArray((resultBundleArray) => {
            let isMatch = true;
            for (const bundle of resultBundleArray) {
                if (this.context.sourceData.bundles[bundle.key]) {
                    if (this.context.sourceData.bundles[bundle.key].md5 !== bundle.md5) {
                        isMatch = false;
                        Editor.error(
                            `[bundle-tool] bundle.json: ${bundle.key} ${
                                this.context.sourceData.bundles[bundle.key].md5
                            } md5 is not match bundle file: ${bundle.md5}`
                        );
                    }
                }
            }
            if (!isMatch) {
                Editor.error(
                    '[bundle-tool] cache cannot sync with bundle file, please delete cache file and reopen bundle-tool'
                );
            }
        });
    }
}

class ExtensionPanelUIHelper {
    static openConfirmDialog(info: string, type = 'info'): void {
        let dialog = Electron.remote.dialog;
        let options = {
            type: type,
            buttons: ['OK'],
            title: type,
            message: info
        };
        if (type === 'error') {
            Electron.shell.beep();
        }
        dialog.showMessageBox(options);
    }

    static openDir(dir: string): void {
        if (fs.existsSync(dir)) {
            Electron.shell.openPath(dir);
            Electron.shell.beep();
        } else {
            Editor.log(`[bundle-tool] directory not exist : ${dir}`);
        }
    }
}

function createVueData(vueContext: BundleToolContext) {
    // 获取项目列表
    let projectOptions: string[] = [];
    let selectedProject = '';
    try {
        projectOptions = ConfigManager.getProjectList();
        selectedProject = projectOptions.length > 0 ? projectOptions[0] : '';
        // 设置当前项目
        if (selectedProject) {
            ConfigManager.setCurrentProject(selectedProject);
        }
    } catch (error) {
        Editor.warn('[bundle-tool] Failed to load project list:', error);
        projectOptions = ['default-project'];
        selectedProject = 'default-project';
    }

    return {
        exportProject: vueContext.project,
        exportEnvironment: vueContext.environment,
        environmentOptions: Context.bundleTool.build.DefaultEnvironmentOptions,
        exportPlatform: vueContext.platformName,
        platformOptions: Context.bundleTool.build.DefaultPlatformOptions,
        sourceDir: vueContext.sourcePath,
        exportDir: vueContext.exportPath,
        manifestData: vueContext.sourceData,
        bundleList: vueContext.bundleArray,
        isProcessing: vueContext.isProcessing,
        statusMessage: '',
        buildTemplateOption: vueContext.buildTemplateOption,
        buildTemplateOptions: Context.cocosCreator.DefaultTemplateOptions,
        configErrorMessage: '',
        supportMacOS: false,
        hideiOSRelatedUI: false,
        // 新增字段
        updateType: UpdateType.BUNDLE, // 默认为 Bundle 更新
        mainBundleData: {
            version: selectedProject ? ConfigManager.getMainPackageVersion(selectedProject) : '1.0.0',
            bundleServerAddress: ''
        } as IMainPackageConfig,
        // 项目选择相关字段
        selectedProject,
        projectOptions
    };
}

// 在 build/pack 前同步 bundleList 到 context.sourceData.bundles
function syncBundleListToSourceData(view: IVueData, context: BundleToolContext) {
    if (!view || !view.bundleList) return;
    view.bundleList.forEach(bundle => {
        if (context.sourceData.bundles[bundle.key]) {
            context.sourceData.bundles[bundle.key].version = bundle.version;
            context.sourceData.bundles[bundle.key].url = bundle.url;
            context.sourceData.bundles[bundle.key].md5 = bundle.md5;
            context.sourceData.bundles[bundle.key].included = bundle.included;
            context.sourceData.bundles[bundle.key].dependencies = bundle.dependencies || [];
        }
    });

    // Editor.info('[bundle-tool] context.sourceData.bundles ' + JSON.stringify(context.sourceData.bundles));
}

// --- Auto sync bundle version/url from config.yaml ---
function syncBundleListFromConfig(
    bundleList: { key: string; version?: string; url?: string; dependencies?: string[]; included?: boolean }[],
    projectName?: string
) {
    try {
        const allBundleConfigs = ConfigManager.getAllBundleConfigs(projectName);
        for (const bundle of bundleList) {
            bundle.version = '';
            bundle.url = '';
            bundle.dependencies = [];

            if (allBundleConfigs[bundle.key]) {
                const conf = allBundleConfigs[bundle.key];
                if (conf.version !== undefined) {
                    bundle.version = conf.version;
                }
                if (conf.url !== undefined) {
                    bundle.url = conf.url;
                }
                if (Array.isArray(conf.dependencies)) {
                    bundle.dependencies = conf.dependencies;
                }
                // 同步 include 状态
                if (conf.include !== undefined) {
                    bundle.included = conf.include;
                }
            }
        }
    } catch (e) {
        Editor.error('[bundle-tool] Failed to sync bundleList from config:', e);
    }
}

function syncBundleListFromConfigToBundleList(
    bundleList: { key: string; version?: string; url?: string }[],
    projectName?: string
) {
    try {
        const allBundleConfigs = ConfigManager.getAllBundleConfigs(projectName);

        for (const bundle of bundleList) {
            const conf = allBundleConfigs[bundle.key];
            if (conf) {
                bundle.version = conf.version ?? bundle.version;
                bundle.url = conf.url ?? bundle.url;
            }
        }
    } catch (error) {
        Editor.error('[bundle-tool] Failed to sync bundle list from config:', error);
    }
}

Editor.Panel.extend({
    style: fs.readFileSync(ContextUI.vue.StylePath, 'utf-8'),
    template: fs.readFileSync(ContextUI.vue.HtmlPath, 'utf-8'),
    ready() {
        const context = new BundleToolContext();
        const extensionPanel = new ExtensionPanel(context);
        const vueData = createVueData(context);
        Editor.info('Editor.Panel.extend vueData', JSON.stringify(vueData));
        // eslint-disable-next-line no-new
        new window.Vue({
            el: this.shadowRoot,
            data: vueData,
            created() {
                const env = {
                    console,
                    context: Context
                };
                let view: IVueData = this as any;

                const sourceEnv = getSourceEnv(env);
                let config: any = null;
                if (sourceEnv) {
                    config = JSON.parse(sourceEnv);
                } else {
                    view.configErrorMessage = 'Please restart CocosCreator for updating temp files';
                    return;
                }

                const envFilePath = ContextConfig.get(context.platformName).bundleTool.getEnvFilePath();

                if (!fs.existsSync(envFilePath)) {
                    view.configErrorMessage =
                        '<div>1. Please create a .env file in project root folder.</div> <div> 2. Setting CocosCreator path and other build config in it.</div>';
                    return;
                }

                if (!config || !config.cocosCreatorPath) {
                    view.configErrorMessage =
                        // eslint-disable-next-line autofix/quotes
                        "<div>1. The cocosCreatorPath of the .env file didn't setting please setting it in .env <div> <div> 2. Restart cocos creator </div>";
                    return;
                }

                if (!fs.existsSync(config.cocosCreatorPath)) {
                    view.configErrorMessage = `Cocos Creator not exist：${config.cocosCreatorPath}`;
                    return;
                }

                extensionPanel.loadCacheFile();

                if (!context.buildTemplateOption) {
                    context.buildTemplateOption = Context.cocosCreator
                        .DefaultTemplateOption as CocosNativeBuildTemplate;
                }

                view.exportProject = context.project;
                view.exportEnvironment = context.environment;
                view.exportPlatform = context.platformName;
                view.buildTemplateOption = context.buildTemplateOption;
                view.sourceDir = context.sourcePath;
                view.exportDir = context.exportPath;
                view.manifestData = context.sourceData;
                view.bundleList = getBundleArray(context.sourceData);
                  
                Editor.info('view.bundleList', JSON.stringify(view.bundleList));
       
                if (os.type() === 'Windows_NT') {
                    view.supportMacOS = false;
                } else {
                    view.supportMacOS = true;
                }
                view.hideiOSRelatedUI = this.isHideiOSRelatedUI();

                // 确保项目配置正确初始化
                const extendedView = view as IVueDataExtended;
                if (extendedView.selectedProject) {
                    try {
                        ConfigManager.setCurrentProject(extendedView.selectedProject);
                        Editor.info(`[bundle-tool] Initialized with project: ${extendedView.selectedProject}`);
                    } catch (error) {
                        Editor.warn('[bundle-tool] Failed to initialize project config:', error);
                    }
                }

                this.fetchBundlesMD5AndUpdateCache(() => {});
            },
            methods: {
                isHideiOSRelatedUI() {
                    let view: IVueData = this as any;
                    return view.exportPlatform === 'ios' && !view.supportMacOS;
                },
                startToProcessing() {
                    let view: IVueData = this as any;
                    view.isProcessing = true;
                },
                stopProcessing() {
                    let view: IVueData = this as any;
                    view.isProcessing = false;
                },
                displayStatusMessage(statusMessage: string) {
                    let view: IVueData = this as any;
                    view.statusMessage = statusMessage;
                },
                fetchBundlesMD5AndUpdateCache(callback: () => void) {
                    const includedBundleKeys: string[] = [];
                    Object.keys(context.sourceData.bundles).forEach((key) => {
                        if (context.sourceData.bundles[key].included) {
                            includedBundleKeys.push(key);
                        }
                    });

                    extensionPanel.fetchBundlesMD5AndRenewBundleArray((resultBundleArray) => {
                        for (const bundle of resultBundleArray) {
                            context.sourceData.bundles[bundle.key] = {
                                version: bundle.version,
                                md5: bundle.md5,
                                url: bundle.url,
                                included: includedBundleKeys.indexOf(bundle.key) > -1,
                                dependencies: bundle.dependencies
                            } as ICacheBundle;
                        }
                        Editor.info('context.sourceData.bundles', JSON.stringify(context.sourceData.bundles));
                        extensionPanel.syncCacheFile();
                        callback && callback();
                    });
                },
                updateNewSourceDir(newPlatform: string, newBuildTemplateOption: CocosNativeBuildTemplate) {
                    let view: IVueData = this as any;

                    const newSourceDir = Context.bundleTool.packConfiguration.getSourcePath(
                        covertExportDevicePlatformToBundlePlatform(newPlatform, newBuildTemplateOption)
                    );
                    view.sourceDir = newSourceDir;
                    context.sourcePath = newSourceDir;

                    const includedBundleKeys: string[] = [];
                    Object.keys(context.sourceData.bundles).forEach((key) => {
                        if (context.sourceData.bundles[key].included) {
                            includedBundleKeys.push(key);
                        }
                    });
                    this.fetchBundlesMD5AndUpdateCache(() => {
                        Object.keys(context.sourceData.bundles).forEach((key) => {
                            context.sourceData.bundles[key].included = includedBundleKeys.indexOf(key) > -1;
                            view.bundleList.forEach((bundle) => {
                                bundle.included = includedBundleKeys.indexOf(bundle.key) > -1;
                            });
                        });
                        view.bundleList = getBundleArray(context.sourceData) as any;
                    });
                },
                // 新增：项目选择变更处理
                onProjectSelectChange(event: cc.Event.EventCustom) {
                    const view: IVueDataExtended = this as any;
                    const selectedProject = event.detail.value;
                    view.selectedProject = selectedProject;

                    try {
                        // 设置当前项目
                        ConfigManager.setCurrentProject(selectedProject);

                        // 同步 context.project
                        context.project = selectedProject;

                        // 更新导出目录
                        const newExportDir = ConfigManager.getExportDirectory(view.updateType, selectedProject);
                        view.exportDir = path.resolve(Editor.Project.path, newExportDir);
                        context.exportPath = view.exportDir;

                        // 更新服务器地址
                        if (view.updateType === UpdateType.MAIN) {
                            view.mainBundleData.bundleServerAddress = ConfigManager.getServerAddress(
                                UpdateType.MAIN,
                                view.exportEnvironment,
                                view.exportPlatform,
                                selectedProject
                            );
                            view.mainBundleData.version = ConfigManager.getMainPackageVersion(selectedProject);
                        } else {
                            context.sourceData.bundleServerAddress = ConfigManager.getServerAddress(
                                UpdateType.BUNDLE,
                                view.exportEnvironment,
                                view.exportPlatform,
                                selectedProject
                            );
                        }

                        // 重新加载 Bundle 列表
                        const bundleList = getBundleArray(context.sourceData) as any;
                        syncBundleListFromConfig(bundleList, selectedProject);
                        view.bundleList = bundleList.slice();
                        syncBundleListToSourceData(view, context);

                        Editor.info(`[bundle-tool] Project changed to: ${selectedProject}`);
                    } catch (error) {
                        Editor.error('[bundle-tool] Failed to switch project:', error);
                    }
                },
                onExportProjectSelectChange(event: cc.Event.EventCustom) {
                    const view: IVueData = this as any;
                    context.project = event.detail.value;
                    view.exportProject = event.detail.value;
                    this.updateBundleServerAddress();
                },
                onExportEnvironmentSelectChange(event: cc.Event.EventCustom) {
                    const view: IVueData = this as any;
                    context.environment = event.detail.value;
                    view.exportEnvironment = event.detail.value;
                    this.updateBundleServerAddress();
                },
                onExportPlatformSelectChange(event: cc.Event.EventCustom) {
                    const view: IVueData = this as any;
                    context.platformName = event.detail.value;
                    view.exportPlatform = event.detail.value;
                    view.hideiOSRelatedUI = this.isHideiOSRelatedUI();
                    this.updateNewSourceDir(event.detail.value, context.buildTemplateOption);
                    this.updateBundleServerAddress();
                },
                onCocosTemplateSelectChange(event: cc.Event.EventCustom) {
                    const newCocosTemplate = event.detail.value;

                    let view: IVueData = this as any;
                    view.buildTemplateOption = newCocosTemplate;
                    context.buildTemplateOption = newCocosTemplate;

                    this.updateNewSourceDir(context.platformName, newCocosTemplate);
                },
                onInputExportPathOver(path: string) {
                    context.exportPath = path;
                },
                onExportDirConfirm() {
                    const result = Editor.Dialog.openFile({
                        title: 'Select Export Directory',
                        defaultPath: context.exportPath,
                        properties: ['openDirectory']
                    });
                    let view: IVueData = this as any;
                    if (result !== -1) {
                        const fullPath = result[0];
                        view.exportDir = fullPath;
                        context.exportPath = view.exportDir;
                    }
                },
                onOpenExportDir() {
                    let view: IVueData = this as any;
                    ExtensionPanelUIHelper.openDir(view.exportDir);
                },
                onInputVersionOver(version: string) {
                    context.sourceData.version = version;
                },
                onInputServerAddressOver(serverAddress: string) {
                    context.sourceData.bundleServerAddress = serverAddress;
                },
                onChangeBundleVersion(version: string, bundleName: string) {
                    if (!context.sourceData.bundles[bundleName]) return;
                    context.sourceData.bundles[bundleName].version = version;
                    let view: IVueData = this as any;
                    const bundle = view.bundleList.find((b) => b.key === bundleName);
                    if (bundle) {
                        bundle.version = version;
                    }
                },
                onChangeBundleMD5(md5: string, bundleName: string) {
                    if (!context.sourceData.bundles[bundleName]) return;
                    context.sourceData.bundles[bundleName].md5 = md5;
                    let view: IVueData = this as any;
                    const bundle = view.bundleList.find((b) => b.key === bundleName);
                    if (bundle) {
                        bundle.md5 = md5;
                    }
                },
                onChangeBundleInclude(included: boolean, bundle: any) {
                    if (!context.sourceData.bundles[bundle.key]) return;
                    context.sourceData.bundles[bundle.key].included = included;
                    let view: IVueData = this as any;
                    const viewBundle = view.bundleList.find((b) => b.key === bundle.key);
                    if (viewBundle) {
                        viewBundle.included = included;
                    }
                    // 同步缓存文件以保存 Include 状态更改
                    extensionPanel.syncCacheFile();
                    Editor.info(`[bundle-tool] Bundle ${bundle.key} include status changed to: ${included}`);
                },
                onChangeBundleUrl(url: string, bundleName: string) {
                    if (!context.sourceData.bundles[bundleName]) return;
                    context.sourceData.bundles[bundleName].url = url;
                    let view: IVueData = this as any;
                    const bundle = view.bundleList.find((b) => b.key === bundleName);
                    if (bundle) {
                        bundle.url = url;
                    }
                },
                // 修改: 依赖变更为字符串输入
                onChangeBundleDependencies(dependenciesStr: string, bundleName: string) {
                    const dependencies = dependenciesStr.split(',').map(s => s.trim()).filter(Boolean);
                    // Editor.info('[bundle-tool] dependencies',dependencies,bundleName);
                    if (!context.sourceData.bundles[bundleName]) return;
                    context.sourceData.bundles[bundleName].dependencies = dependencies;
                    let view: IVueData = this as any;
                    const bundle = view.bundleList.find((b) => b.key === bundleName);
                    if (bundle) {
                        bundle.dependencies = dependencies;
                    }
                },
                updateBundleServerAddress(): void {
                    const view: IVueDataExtended = this as any;
                    try {
                        const currentProject = view.selectedProject || ConfigManager.getCurrentProject() || undefined;
                        context.sourceData.bundleServerAddress = ConfigManager.getServerAddress(
                            UpdateType.BUNDLE,
                            view.exportEnvironment,
                            view.exportPlatform,
                            currentProject
                        );
                    } catch (error) {
                        Editor.error('[bundle-tool] Failed to get bundle server address:', error);
                        context.sourceData.bundleServerAddress = '';
                    }

                    const bundleList = getBundleArray(context.sourceData) as any;
                    // 自动同步 version/url
                    const currentProject = view.selectedProject || ConfigManager.getCurrentProject() || undefined;
                    syncBundleListFromConfig(bundleList, currentProject);
                    view.bundleList = bundleList.slice();
                    syncBundleListToSourceData(view, context);

                    this.fetchBundlesMD5AndUpdateCache(() => {});
                },
                // 新增：更新类型切换处理
                onUpdateTypeChange() {
                    let view: IVueDataExtended = this as any;
                    try {
                        const currentProject = view.selectedProject || ConfigManager.getCurrentProject() || undefined;

                        // 更新导出目录
                        const newExportDir = ConfigManager.getExportDirectory(view.updateType, currentProject);
                        view.exportDir = path.resolve(Editor.Project.path, newExportDir);
                        context.exportPath = view.exportDir;

                        // 更新服务器地址
                        if (view.updateType === UpdateType.MAIN) {
                            view.mainBundleData.bundleServerAddress = ConfigManager.getServerAddress(
                                UpdateType.MAIN,
                                view.exportEnvironment,
                                view.exportPlatform,
                                currentProject
                            );
                        } else {
                            context.sourceData.bundleServerAddress = ConfigManager.getServerAddress(
                                UpdateType.BUNDLE,
                                view.exportEnvironment,
                                view.exportPlatform,
                                currentProject
                            );
                        }

                        Editor.info(`[bundle-tool] Update type changed to: ${view.updateType}, export dir: ${view.exportDir}`);
                    } catch (error) {
                        Editor.error('[bundle-tool] Failed to update configuration:', error);
                    }
                },
                // 新增：主包服务器地址输入处理
                onInputMainBundleServerAddressOver(serverAddress: string) {
                    let view: IVueDataExtended = this as any;
                    view.mainBundleData.bundleServerAddress = serverAddress;
                },
                // 新增：主包版本输入处理
                onInputMainVersionOver(version: string) {
                    let view: IVueDataExtended = this as any;
                    view.mainBundleData.version = version;
                },
                build(callback: (preUIBundles: any, firstExecution: boolean, errorMessage: string) => void) {
                    // 在 build/pack 方法内最前面调用
                    let view: IVueData = this as any;
                    syncBundleListToSourceData(view, context);

                    const preUIBundles = { ...context.sourceData.bundles };
                    let firstExecution = view.bundleList.length === 0;

                    const env = {
                        console: new EditorConsole(),
                        context: Context
                    };
                    const directory = Editor.Project.path;

                    buildProject(
                        env,
                        directory,
                        context.project,
                        context.platformName,
                        context.buildTemplateOption,
                        (info: string, error) => {
                            if (error) {
                                Editor.error('[bundle-tool] error: ' + info);
                                ExtensionPanelUIHelper.openConfirmDialog(info, 'error');
                                callback(null, firstExecution, info);
                                return;
                            }

                            context.sourcePath = Context.cocosCreator.getBuiltPlatformPathAsset(
                                covertExportDevicePlatformToBundlePlatform(
                                    context.platformName,
                                    context.buildTemplateOption
                                )
                            );
                            view.sourceDir = context.sourcePath;

                            const BundleNameOfDirectoryArray = getDirectories(context.sourcePath);
                            const newBundles: Record<string, any> = {};
                            const newBundleArray: any[] = [];
                            for (const bundleName of BundleNameOfDirectoryArray) {
                                const newBundle = extensionPanel.getNewBundle(context.sourcePath, bundleName);
                                newBundleArray.push(newBundle);
                                newBundles[bundleName] = newBundle;
                            }
                            context.bundleArray = newBundleArray;
                            context.sourceData.bundles = newBundles;

                            this.fetchBundlesMD5AndUpdateCache(() => {
                                Editor.info('[bundle-tool] info ' + info);
                                const bundleList = getBundleArray(context.sourceData) as any;
                                // 自动同步 version/url
                                const currentProject = (view as IVueDataExtended).selectedProject || ConfigManager.getCurrentProject() || undefined;
                                syncBundleListFromConfig(bundleList, currentProject);
                                view.bundleList = bundleList.slice();
                                syncBundleListToSourceData(view, context);
                                callback(preUIBundles, firstExecution, '');
                                extensionPanel.syncCacheFile(); // 新增這一行
                            });
                        }
                    );
                },
                pack(
                    preUIBundles: PartialRecord<string, { included: boolean }> | null,
                    firstExecution: boolean,
                    callback: (errorMessage: string) => void
                ) {
                    // 在 build/pack 方法内最前面调用
                    let view: IVueData = this as any;
                    syncBundleListToSourceData(view, context);

                    if (!fs.existsSync(context.sourcePath)) {
                        const message = `Please build first. directory not exist : ${context.sourcePath}`;
                        Editor.log(`[bundle-tool] ${message}`);
                        ExtensionPanelUIHelper.openConfirmDialog(message);
                        callback(message);
                        return;
                    }

                    const targetPath = path.join(context.exportPath);

                    // Clean target directory and httpserver public directory before pack
                    cleanPackDirectories(targetPath, Editor.Project.path);
                    let packedBundle: IBundleSourceData = JSON.parse(
                        JSON.stringify(context.sourceData)
                    ) as ICacheSourceData;
                    const ignoreBundleNameArray: string[] = [];

                    Object.keys(context.sourceData.bundles).forEach((bundleKey) => {
                        let bundle = context.sourceData.bundles[bundleKey];
                        if (preUIBundles) {
                            const prebundle = preUIBundles[bundleKey];
                            if (prebundle) {
                                bundle.included = prebundle.included;
                            }
                        }
                        if (firstExecution) {
                            bundle.included = true;
                        }
                        if (bundle.included) {
                            const targetBundle = view.bundleList.find((bundle2) => bundle2.key === bundleKey);
                            if (targetBundle) {
                                packedBundle.bundles[targetBundle.key] = {
                                    version: targetBundle.version,
                                    md5: targetBundle.md5,
                                    url: targetBundle.url || '',
                                    dependencies: targetBundle.dependencies || []
                                };
                                context.sourceData.bundles[targetBundle.key] = {
                                    ...packedBundle.bundles[targetBundle.key],
                                    included: true
                                };
                            }
                        } else {
                            delete packedBundle.bundles[bundleKey];
                            ignoreBundleNameArray.push(bundleKey);
                        }
                    });

                    view.bundleList = Object.keys(context.sourceData.bundles).map((key) => {
                        const bundle = context.sourceData.bundles[key];
                        return { key, ...bundle, included: bundle.included };
                    });

                    copyBundle(context.sourcePath, targetPath, packedBundle.bundles, (info: string) => {
                        Editor.info('[bundle-tool] info' + info);

                        const str = JSON.stringify(packedBundle, null, 2);
                        writeJSONFile(Context.bundleTool.packConfiguration.getBundleJSONPath(targetPath), str);

                        if (context.platformName !== DevicePlatform.WEB_MOBILE) {
                            generateVersionManifestFile(
                                {
                                    sourceData: context.sourceData,
                                    Context,
                                    platformName: context.platformName,
                                    buildTemplateOption: context.buildTemplateOption,
                                    exportPath: context.exportPath
                                },
                                ignoreBundleNameArray,
                                () => {
                                    callback('');
                                    extensionPanel.syncCacheFile(); // 新增這一行
                                }
                            );
                        } else {
                            callback('');
                            extensionPanel.syncCacheFile(); // 新增這一行
                        }
                    });
                },
                onBuildProjectBundles() {
                    let view: IVueDataExtended = this as any;
                    this.startToProcessing();
                    this.displayStatusMessage('Building...');

                    if (view.updateType === UpdateType.MAIN) {
                        // 主包构建逻辑
                        this.buildMainPackage((errorMessage: string) => {
                            if (errorMessage === '') {
                                this.displayStatusMessage('Main Package Build Successfully.');
                            } else {
                                this.displayStatusMessage(errorMessage);
                            }
                            this.stopProcessing();
                        });
                    } else {
                        // Bundle 构建逻辑
                        this.build((_preUIBundles: any, _firstExecution: boolean, errorMessage: string) => {
                            if (errorMessage === '') {
                                this.displayStatusMessage('Build Successfully.');
                                this.stopProcessing();
                            } else {
                                this.displayStatusMessage(errorMessage);
                            }
                            this.stopProcessing();
                        });
                    }
                },
                onPackProjectBundles() {
                    let view: IVueDataExtended = this as any;
                    this.startToProcessing();
                    this.displayStatusMessage('Packing...');

                    if (view.updateType === UpdateType.MAIN) {
                        // 主包打包逻辑
                        this.packMainPackage((errorMessage: string) => {
                            if (errorMessage === '') {
                                setTimeout(() => {
                                    this.displayStatusMessage('Main Package Packed Successfully.');
                                    this.stopProcessing();
                                }, 500);
                            } else {
                                setTimeout(() => {
                                    this.displayStatusMessage(errorMessage);
                                    this.stopProcessing();
                                }, 500);
                            }
                        });
                    } else {
                        // Bundle 打包逻辑
                        this.pack(null, false, (errorMessage: string) => {
                            if (errorMessage === '') {
                                setTimeout(() => {
                                    this.displayStatusMessage('Packed Successfully.');
                                    this.stopProcessing();
                                }, 500);
                            } else {
                                setTimeout(() => {
                                    this.displayStatusMessage(errorMessage);
                                    this.stopProcessing();
                                }, 500);
                            }
                        });
                    }
                },
                onBuildAndPackProjectBundles() {
                    let view: IVueDataExtended = this as any;
                    this.startToProcessing();
                    this.displayStatusMessage('Building...');

                    if (view.updateType === UpdateType.MAIN) {
                        // 主包构建和打包逻辑
                        this.buildMainPackage((errorMessage: string) => {
                            if (errorMessage === '') {
                                this.displayStatusMessage('Packing...');
                                this.packMainPackage((errorMessage: string) => {
                                    if (errorMessage === '') {
                                        setTimeout(() => {
                                            this.displayStatusMessage('Main Package Build and Pack Successfully.');
                                            this.stopProcessing();
                                        }, 500);
                                    } else {
                                        setTimeout(() => {
                                            this.displayStatusMessage(errorMessage);
                                            this.stopProcessing();
                                        }, 500);
                                    }
                                });
                            } else {
                                this.displayStatusMessage(errorMessage);
                                this.stopProcessing();
                            }
                        });
                    } else {
                        // Bundle 构建和打包逻辑
                        this.build((preUIBundles: any, firstExecution: boolean, errorMessage: string) => {
                            if (errorMessage === '') {
                                this.displayStatusMessage('Packing...');
                                this.pack(preUIBundles, firstExecution, (errorMessage: string) => {
                                    if (errorMessage === '') {
                                        setTimeout(() => {
                                            this.displayStatusMessage('Packed Successfully.');
                                            this.stopProcessing();
                                        }, 500);
                                    } else {
                                        setTimeout(() => {
                                            this.displayStatusMessage(errorMessage);
                                            this.stopProcessing();
                                        }, 500);
                                    }
                                });
                            } else {
                                this.displayStatusMessage(errorMessage);
                                this.stopProcessing();
                            }
                        });
                    }
                },
                onPushLocalServer() {
                    const view: IVueDataExtended = this as any;
                    this.startToProcessing();
                    this.displayStatusMessage('Starting local servers...');

                    try {
                        // 导入 LocalServerManager
                        const { LocalServerManager } = require('./local-server-manager');
                        const serverManager = new LocalServerManager(new EditorConsole(), Editor.Project.path);

                        // 获取构建产物路径
                        const projectName = view.selectedProject || 'pkw-game';
                        const buildBasePath = path.join(Editor.Project.path, 'build', projectName);

                        // Push Local Server 功能统一处理主包和子包，不区分更新类型
                        const mainPackagePath = path.join(buildBasePath, 'main-package');
                        const bundlesPath = path.join(buildBasePath, 'bundles');
                        const bundleJsonPath = path.join(bundlesPath, 'bundle.json');

                        // 检查必要文件是否存在
                        const hasMainPackage = fs.existsSync(mainPackagePath);
                        const hasBundles = fs.existsSync(bundlesPath);
                        const hasBundleJson = fs.existsSync(bundleJsonPath);

                        if (!hasMainPackage && !hasBundles) {
                            throw new Error(`Neither main package nor bundles found. Please build first.\nMain package: ${mainPackagePath}\nBundles: ${bundlesPath}`);
                        }

                        // 如果没有 bundle.json 但有 bundles 目录，尝试使用空的 bundle.json
                        let finalBundleJsonPath = bundleJsonPath;
                        if (!hasBundleJson && hasBundles) {
                            Editor.warn('[bundle-tool] bundle.json not found, will scan bundles directory directly');
                            finalBundleJsonPath = ''; // 传递空字符串，让服务器直接扫描目录
                        }

                        // 启动本地服务器 - 统一处理主包和子包
                        serverManager.startLocalServers(finalBundleJsonPath, mainPackagePath, bundlesPath)
                            .then(() => {
                                setTimeout(() => {
                                    this.displayStatusMessage('Local servers started successfully! Check console for URLs.');
                                    this.stopProcessing();
                                }, 1000);
                            })
                            .catch((error: Error) => {
                                setTimeout(() => {
                                    this.displayStatusMessage(`Failed to start local servers: ${error.message}`);
                                    this.stopProcessing();
                                }, 500);
                            });

                    } catch (error) {
                        setTimeout(() => {
                            this.displayStatusMessage(`Error: ${(error as Error).message}`);
                            this.stopProcessing();
                        }, 500);
                    }
                },
                // 新增：主包构建方法
                buildMainPackage(callback: (errorMessage: string) => void) {
                    // 保存当前 UI 状态到临时文件，供 quick-build.js 使用
                    const view: IVueDataExtended = this as any;
                    this.saveUIStateForQuickBuild(view);

                    const env = {
                        console: new EditorConsole(),
                        context: Context
                    };
                    const directory = Editor.Project.path;

                    buildProject(
                        env,
                        directory,
                        context.project,
                        context.platformName,
                        context.buildTemplateOption,
                        (info: string, error) => {
                            if (error) {
                                Editor.error('[bundle-tool] Main package build error: ' + info);
                                ExtensionPanelUIHelper.openConfirmDialog(info, 'error');
                                callback(info);
                                return;
                            }

                            Editor.info('[bundle-tool] Main package build info: ' + info);
                            callback('');
                        }
                    );
                },
                // 新增：主包打包方法
                packMainPackage(callback: (errorMessage: string) => void) {
                    let view: IVueDataExtended = this as any;

                    const sourcePath = Context.cocosCreator.getBuiltPlatformPath(
                        covertExportDevicePlatformToBundlePlatform(
                            context.platformName,
                            context.buildTemplateOption
                        )
                    );

                    if (!fs.existsSync(sourcePath)) {
                        const message = `Please build main package first. directory not exist : ${sourcePath}`;
                        Editor.log(`[bundle-tool] ${message}`);
                        ExtensionPanelUIHelper.openConfirmDialog(message);
                        callback(message);
                        return;
                    }

                    // Clean target directory and httpserver public directory before pack
                    cleanPackDirectories(context.exportPath, Editor.Project.path);

                    // 确保导出目录存在
                    if (!fs.existsSync(context.exportPath)) {
                        fs.mkdirSync(context.exportPath, { recursive: true });
                    }

                    generateMainPackageManifestFile(
                        {
                            mainBundleData: view.mainBundleData,
                            Context,
                            platformName: context.platformName,
                            buildTemplateOption: context.buildTemplateOption,
                            exportPath: context.exportPath,
                            selectedProject: view.selectedProject
                        },
                        () => {
                            Editor.info('[bundle-tool] Main package manifest generated successfully');

                            // 复制主包文件到导出目录
                            this.copyMainPackageFiles(sourcePath, context.exportPath, (copyError: string) => {
                                if (copyError) {
                                    Editor.error('[bundle-tool] Failed to copy main package files:', copyError);
                                    callback(copyError);
                                } else {
                                    Editor.info('[bundle-tool] Main package files copied successfully');
                                    callback('');
                                }
                            });
                        }
                    );
                },
                // 新增：复制主包文件方法
                copyMainPackageFiles(sourcePath: string, exportPath: string, callback: (errorMessage: string) => void) {
                    try {
                        const view: IVueDataExtended = this as any;

                        // 复制 src 目录
                        const srcSourcePath = path.join(sourcePath, 'src');
                        const srcTargetPath = path.join(exportPath, 'src');
                        if (fs.existsSync(srcSourcePath)) {
                            this.copyDirectoryRecursive(srcSourcePath, srcTargetPath);
                            Editor.info(`[bundle-tool] Copied src directory from ${srcSourcePath} to ${srcTargetPath}`);
                        }

                        // 复制 assets 目录中的资源，根据项目配置条件化处理
                        const assetsSourcePath = path.join(sourcePath, 'assets');
                        const assetsTargetPath = path.join(exportPath, 'assets');
                        if (fs.existsSync(assetsSourcePath)) {
                            // 确保目标 assets 目录存在
                            if (!fs.existsSync(assetsTargetPath)) {
                                fs.mkdirSync(assetsTargetPath, { recursive: true });
                            }

                            // 获取 UI 中勾选 include 的 bundle 列表（这些 bundle 应该只在 bundles 文件夹中，不在主包中）
                            let includedBundles: string[] = [];
                            if (view.bundleList) {
                                for (const bundle of view.bundleList) {
                                    if (bundle.included === true) {
                                        includedBundles.push(bundle.key);
                                    }
                                }
                            }
                            Editor.info(`[bundle-tool] Bundles marked as included (will be excluded from main package): ${includedBundles.join(', ')}`);

                            // 处理 assets 目录中的所有项目
                            const assetItems = fs.readdirSync(assetsSourcePath);
                            for (const item of assetItems) {
                                const itemPath = path.join(assetsSourcePath, item);
                                const stat = fs.statSync(itemPath);

                                if (stat.isDirectory()) {
                                    // 检查是否为主包资源目录
                                    if (['internal', 'main', 'resources'].includes(item)) {
                                        const targetPath = path.join(assetsTargetPath, item);
                                        this.copyDirectoryRecursive(itemPath, targetPath);
                                        Editor.info(`[bundle-tool] Copied main package directory ${item} from ${itemPath} to ${targetPath}`);
                                    } else if (includedBundles.includes(item)) {
                                        // 跳过在 UI 中勾选了 include 的 bundle（这些 bundle 应该只在 bundles 文件夹中）
                                        Editor.info(`[bundle-tool] Skipped bundle directory ${item} (marked as included, will be in bundles folder only)`);
                                    } else {
                                        // 复制未勾选 include 的 bundle 目录到主包
                                        const targetPath = path.join(assetsTargetPath, item);
                                        this.copyDirectoryRecursive(itemPath, targetPath);
                                        Editor.info(`[bundle-tool] Copied bundle directory ${item} to main package (not marked as included)`);
                                    }
                                } else if (stat.isFile()) {
                                    // 复制 assets 根目录下的文件
                                    const targetFilePath = path.join(assetsTargetPath, item);
                                    fs.copyFileSync(itemPath, targetFilePath);
                                    Editor.info(`[bundle-tool] Copied assets root file ${item} to ${targetFilePath}`);
                                }
                            }
                        }

                        callback('');
                    } catch (error) {
                        const errorMessage = `Failed to copy main package files: ${error}`;
                        Editor.error(`[bundle-tool] ${errorMessage}`);
                        callback(errorMessage);
                    }
                },
                // 新增：递归复制目录的辅助方法
                copyDirectoryRecursive(source: string, target: string) {
                    if (!fs.existsSync(target)) {
                        fs.mkdirSync(target, { recursive: true });
                    }

                    const items = fs.readdirSync(source);
                    for (const item of items) {
                        const sourcePath = path.join(source, item);
                        const targetPath = path.join(target, item);
                        const stat = fs.statSync(sourcePath);

                        if (stat.isDirectory()) {
                            this.copyDirectoryRecursive(sourcePath, targetPath);
                        } else {
                            fs.copyFileSync(sourcePath, targetPath);
                        }
                    }
                },
                // 新增：保存 UI 状态供 quick-build.js 使用
                saveUIStateForQuickBuild(view: IVueDataExtended) {
                    try {
                        const uiState = {
                            bundleIncludeStates: {} as Record<string, boolean>,
                            timestamp: Date.now()
                        };

                        // 收集所有 bundle 的 include 状态
                        if (view.bundleList) {
                            for (const bundle of view.bundleList) {
                                uiState.bundleIncludeStates[bundle.key] = bundle.included === true;
                            }
                        }

                        // 写入临时文件
                        const tempDir = path.join(Editor.Project.path, 'temp');
                        if (!fs.existsSync(tempDir)) {
                            fs.mkdirSync(tempDir, { recursive: true });
                        }

                        const uiStatePath = path.join(tempDir, 'bundle-ui-state.json');
                        fs.writeFileSync(uiStatePath, JSON.stringify(uiState, null, 2));

                        Editor.info(`[bundle-tool] Saved UI state to ${uiStatePath}`);
                        Editor.info(`[bundle-tool] Bundle include states: ${JSON.stringify(uiState.bundleIncludeStates)}`);

                    } catch (error) {
                        Editor.error(`[bundle-tool] Failed to save UI state: ${error}`);
                    }
                }
            }
        });
    }
});
