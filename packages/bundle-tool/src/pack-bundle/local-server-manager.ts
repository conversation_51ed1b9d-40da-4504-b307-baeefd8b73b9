import * as fs from 'fs';
import * as path from 'path';
import { spawn } from 'child_process';
import type { ChildProcess } from 'child_process';
import type { EditorConsole } from '../type';

export interface IServerConfig {
    mainPackage: {
        port: number;
        path: string;
    };
    bundles: {
        name: string;
        port: number;
        path: string;
    }[];
}

export interface IBundleJson {
    version: string;
    remoteManifestUrl: string;
    bundleServerAddress: string;
    bundles: Record<string, {
        key: string;
        md5: string;
        version: string;
        url: string;
        dependencies: string[];
    }>;
}

export class LocalServerManager {
    private console: EditorConsole;
    private runningProcesses: Map<number, ChildProcess> = new Map();
    private serverConfigPath: string;

    constructor(console: EditorConsole, projectPath: string) {
        this.console = console;
        this.serverConfigPath = path.join(projectPath, 'packages', 'bundle-tool', 'httpserver');
    }

    /**
     * 从 bundle.json 解析服务器配置
     */
    parseServerConfig(bundleJsonPath: string, mainPackagePath: string): IServerConfig {
        const config: IServerConfig = {
            mainPackage: {
                port: 3000,
                path: mainPackagePath
            },
            bundles: []
        };

        try {
            if (fs.existsSync(bundleJsonPath)) {
                const bundleData: IBundleJson = JSON.parse(fs.readFileSync(bundleJsonPath, 'utf-8'));
                
                // 解析主包端口（从 bundleServerAddress 提取）
                if (bundleData.bundleServerAddress) {
                    try {
                        const mainUrl = new URL(bundleData.bundleServerAddress);
                        const mainPort = parseInt(mainUrl.port, 10) || 3000;
                        config.mainPackage.port = mainPort;
                    } catch (error) {
                        this.console.warn(`Invalid main package URL: ${bundleData.bundleServerAddress}`);
                    }
                }

                // 解析每个 bundle 的端口
                if (bundleData.bundles) {
                    for (const [bundleName, bundleInfo] of Object.entries(bundleData.bundles)) {
                        if (bundleInfo.url) {
                            try {
                                const bundleUrl = new URL(bundleInfo.url);
                                const bundlePort = parseInt(bundleUrl.port, 10) || 3001;
                                const bundlePath = path.dirname(bundleJsonPath); // bundles 目录
                                
                                config.bundles.push({
                                    name: bundleName,
                                    port: bundlePort,
                                    path: bundlePath
                                });
                            } catch (error) {
                                this.console.warn(`Invalid URL for bundle ${bundleName}: ${bundleInfo.url}`);
                            }
                        }
                    }
                }
            }
        } catch (error) {
            this.console.error(`Error parsing bundle.json: ${error}`);
        }

        return config;
    }

    /**
     * 复制文件到服务器目录
     */
    async copyToServerDirectory(sourcePath: string, targetPath: string): Promise<void> {
        return new Promise((resolve, reject) => {
            // 确保目标目录存在
            const targetDir = path.dirname(targetPath);
            if (!fs.existsSync(targetDir)) {
                fs.mkdirSync(targetDir, { recursive: true });
            }

            // 使用 cp 命令复制文件
            const copyProcess = spawn('cp', ['-r', sourcePath, targetPath], {
                stdio: 'pipe'
            });

            copyProcess.on('close', (code) => {
                if (code === 0) {
                    this.console.info(`Copied: ${sourcePath} -> ${targetPath}`);
                    resolve();
                } else {
                    const error = new Error(`Copy failed with code ${code}`);
                    this.console.error(`Copy failed: ${sourcePath} -> ${targetPath} - ${error.message}`);
                    reject(error);
                }
            });

            copyProcess.on('error', (error) => {
                this.console.error(`Copy error: ${sourcePath} -> ${targetPath} - ${error.message}`);
                reject(error);
            });
        });
    }

    /**
     * 杀死指定端口的进程
     */
    async killPortProcess(port: number): Promise<boolean> {
        return new Promise((resolve) => {
            this.console.info(`Checking for processes on port ${port}...`);

            const killCommand = process.platform === 'win32'
                ? `netstat -ano | findstr :${port}`
                : `lsof -ti:${port}`;

            const killProcess = spawn('sh', ['-c', killCommand], { stdio: 'pipe' });
            let hasProcess = false;

            killProcess.stdout.on('data', (data) => {
                const output = data.toString().trim();
                if (output) {
                    hasProcess = true;
                    const pids = output.split('\n').filter((pid: string) => pid.trim());

                    for (const pid of pids) {
                        const finalKillCommand = process.platform === 'win32'
                            ? `taskkill /PID ${pid.trim()} /F`
                            : `kill -9 ${pid.trim()}`;

                        try {
                            spawn('sh', ['-c', finalKillCommand], { stdio: 'inherit' });
                            this.console.info(`Killed process ${pid.trim()} on port ${port}`);
                        } catch (error) {
                            this.console.warn(`Failed to kill process ${pid.trim()}: ${error}`);
                        }
                    }
                }
            });

            killProcess.on('close', () => {
                if (hasProcess) {
                    this.console.info(`Port ${port} cleanup completed`);
                    // 等待一下让端口完全释放
                    setTimeout(() => resolve(true), 1000);
                } else {
                    this.console.info(`Port ${port} is free`);
                    resolve(false);
                }
            });

            killProcess.on('error', (error) => {
                this.console.warn(`Error checking port ${port}: ${error.message}`);
                resolve(false);
            });
        });
    }

    /**
     * 清理常用端口
     */
    async cleanupCommonPorts(): Promise<void> {
        const commonPorts = [30001, 30002, 30003, 30004, 30005, 30006, 30007, 30008];
        this.console.info('🧹 Cleaning up common server ports...');

        for (const port of commonPorts) {
            await this.killPortProcess(port);
        }

        this.console.info('✅ Port cleanup completed');
    }

    /**
     * 清理httpserver public目录
     */
    private cleanPublicDirectory(): void {
        const publicPath = path.join(this.serverConfigPath, 'public');
        if (fs.existsSync(publicPath)) {
            fs.rmSync(publicPath, { recursive: true, force: true });
            this.console.info(`🧹 Cleaned httpserver public directory: ${publicPath}`);
        }
        // 重新创建public目录
        fs.mkdirSync(publicPath, { recursive: true });
    }

    /**
     * 启动本地服务器
     */
    async startLocalServers(bundleJsonPath: string, mainPackagePath: string, bundlesPath: string): Promise<void> {
        try {
            this.console.info('🚀 Starting local servers with auto port assignment...');

            // 清理httpserver public目录
            this.cleanPublicDirectory();

            // 先清理可能占用的端口
            await this.cleanupCommonPorts();

            // 启动服务器进程
            const serverScript = path.join(this.serverConfigPath, 'server.js');
            const serverProcess = spawn('node', [
                serverScript,
                bundleJsonPath,
                mainPackagePath,
                bundlesPath
            ], {
                stdio: 'pipe',
                cwd: this.serverConfigPath
            });

            // 等待服务器启动完成
            await new Promise<void>((resolve, reject) => {
                let serverStarted = false;
                let errorOccurred = false;

                // 处理服务器输出
                serverProcess.stdout?.on('data', (data) => {
                    const output = data.toString();
                    this.console.info(`[Server] ${output.trim()}`);

                    // 检查服务器是否成功启动
                    if (output.includes('🎉 All servers started successfully!')) {
                        serverStarted = true;
                        if (!errorOccurred) {
                            resolve();
                        }
                    }
                });

                serverProcess.stderr?.on('data', (data) => {
                    const output = data.toString();
                    this.console.warn(`[Server Error] ${output.trim()}`);

                    // 检查是否是端口占用错误
                    if (output.includes('EADDRINUSE') || output.includes('address already in use')) {
                        errorOccurred = true;
                        reject(new Error(`Port already in use: ${output.trim()}`));
                    }
                });

                serverProcess.on('error', (error) => {
                    errorOccurred = true;
                    this.console.error(`Failed to start local server: ${error.message}`);
                    reject(error);
                });

                serverProcess.on('close', (code) => {
                    if (code === 0) {
                        this.console.info('Local server process completed successfully');
                        if (!serverStarted && !errorOccurred) {
                            resolve();
                        }
                    } else {
                        errorOccurred = true;
                        const error = new Error(`Local server process exited with code ${code}`);
                        this.console.error(error.message);
                        reject(error);
                    }
                });

                // 记录进程以便后续管理
                const processId = Date.now();
                this.runningProcesses.set(processId, serverProcess);

                // 超时处理
                setTimeout(() => {
                    if (!serverStarted && !errorOccurred) {
                        errorOccurred = true;
                        reject(new Error('Server startup timeout'));
                    }
                }, 30000); // 30秒超时
            });

            this.console.info('🎉 Local servers started successfully!');
            this.console.info('📋 Server URLs:');
            this.console.info('   • Check console output above for specific URLs');
            this.console.info('   • Main Package: http://192.168.x.x:30001/');
            this.console.info('   • Bundles: http://192.168.x.x:30002+/');

        } catch (error: any) {
            this.console.error(`Failed to start local servers: ${error.message}`);

            // 如果是端口占用错误，尝试重新清理端口并重试
            if (error.message.includes('Port already in use') || error.message.includes('EADDRINUSE')) {
                this.console.warn('🔄 Port conflict detected, attempting to retry...');

                try {
                    // 更彻底的端口清理
                    await this.cleanupCommonPorts();
                    await new Promise<void>(resolve => {
                        setTimeout(() => resolve(), 2000);
                    });

                    // 重试一次
                    this.console.info('🔄 Retrying server startup...');
                    await this.startLocalServers(bundleJsonPath, mainPackagePath, bundlesPath);
                    return;
                } catch (retryError: any) {
                    this.console.error(`Retry failed: ${retryError.message}`);
                    throw retryError;
                }
            }

            throw error;
        }
    }

    /**
     * 停止所有运行中的服务器
     */
    stopAllServers(): void {
        for (const [id, process] of this.runningProcesses) {
            try {
                process.kill('SIGTERM');
                this.runningProcesses.delete(id);
                this.console.info(`Stopped server process ${id}`);
            } catch (error) {
                this.console.warn(`Failed to stop server process ${id}: ${(error as Error).message}`);
            }
        }
    }
}
