# PKW Game 项目配置文件
# 包含主包更新和Bundle更新的完整配置

# XXTEA加密密钥配置
xxtea_key: "05279ccb-8728-4a"

# 主包更新配置
main_package:
  # 主包版本号
  version: "1.0.0"
  # 主包服务器地址配置
  server_addresses:
    DEV:
      WEB_MOBILE: "https://frontend.dev.liuxinyi1.cn/dev/pkw-bundles/0_2_0/h5/"
      IOS: "https://frontend.dev.liuxinyi1.cn/dev/pkw-bundles/0_2_0/native/"
      ANDROID: "https://frontend.dev.liuxinyi1.cn/dev/pkw-bundles/0_2_0/native/"
    STAGE:
      WEB_MOBILE: "https://frontend.stg.liuxinyi1.cn/dev/pkw-bundles/0_2_0/h5/"
      IOS: "https://frontend.stg.liuxinyi1.cn/dev/pkw-bundles/0_2_0/native/"
      ANDROID: "https://frontend.stg.liuxinyi1.cn/dev/pkw-bundles/0_2_0/native/"
    PROD:
      WEB_MOBILE: "https://h5.nianpxk.com/assets_a92/bundles/0_2_0/h5/"
      IOS: "https://h5.nianpxk.com/assets_a92/bundles/0_2_0/native/"
      ANDROID: "https://h5.nianpxk.com/assets_a92/bundles/0_2_0/native/"
  # 主包导出目录配置
  export_directory: "./build/pkw-game/main-package"

# Bundle更新配置
bundle_update:
  # Bundle导出目录配置
  export_directory: "./build/pkw-game/bundles"
  # Bundle服务器地址配置
  server_addresses:
    DEV:
      WEB_MOBILE: "https://frontend.dev.liuxinyi1.cn/dev/pkw-bundles/0_2_0/h5/"
      IOS: "https://frontend.dev.liuxinyi1.cn/dev/pkw-bundles/0_2_0/native/"
      ANDROID: "https://frontend.dev.liuxinyi1.cn/dev/pkw-bundles/0_2_0/native/"
    STAGE:
      WEB_MOBILE: "https://frontend.stg.liuxinyi1.cn/dev/pkw-bundles/0_2_0/h5/"
      IOS: "https://frontend.stg.liuxinyi1.cn/dev/pkw-bundles/0_2_0/native/"
      ANDROID: "https://frontend.stg.liuxinyi1.cn/dev/pkw-bundles/0_2_0/native/"
    PROD:
      WEB_MOBILE: "https://h5.nianpxk.com/assets_a92/bundles/0_2_0/h5/"
      IOS: "https://h5.nianpxk.com/assets_a92/bundles/0_2_0/native/"
      ANDROID: "https://h5.nianpxk.com/assets_a92/bundles/0_2_0/native/"
  # Bundle详细配置
  bundles:
    common-resource:
      version: "1.0"
      md5: ""
      url: "http://************:30002/"
      include: true
      dependencies: []
    common-portrait:
      version: "1.1"
      md5: ""
      url: "http://************:30003/"
      include: true
      dependencies:
        - "common"
    common-landscape:
      version: "1.1"
      md5: ""
      url: "http://************:30002/"
      include: false
      dependencies:
        - "common"
    cowboy:
      version: "3.0"
      md5: ""
      url: "http://************:30002/"
      include: false
      dependencies:
        - "lobby-portrait"
    poker-master:
      version: "3.0"
      md5: ""
      url: "http://************:30002/"
      include: false
      dependencies:
        - "lobby-portrait"
